Header always set X-Content-Type-Options: nosniff
Header always set Strict-Transport-Security "max-age=63072000;"
Header always set X-Xss-Protection "1; mode=block"
Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' protection.clickguardian.co.uk snap.licdn.com *.ads.linkedin.com secure.adnxs.com; img-src data: 'self' 'unsafe-inline' ssl.google-analytics.com http: *.autodesk.net *.autodesk.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' ssl.google-analytics.com protection.clickguardian.co.uk snap.licdn.com secure.adnxs.com www.googleadservices.com linkedin.com px.ads.linkedin.com *.linkedin.com use.fontawesome.com; child-src www.youtube.com; style-src 'self' 'unsafe-inline' maxcdn.bootstrapcdn.com;font-src 'self' 'unsafe-inline' maxcdn.bootstrapcdn.com use.fontawesome.com;"
Header always set Referrer-Policy "origin-when-cross-origin"
Header always set X-Frame-Options "ALLOW-FROM https://youtube.com/"


Order Allow,Deny
Allow from all

<IfModule mod_rewrite.c>
  RewriteEngine On

  # RewriteBase inMozilla/5.0 (compatible; Google-Structured-Data-Testing-Tool +https://search.google.com/structured-data/testing-tool)"tions
  # Change RewriteBase dependent on how your shop is accessed as below.
  # http://www.mysite.com = RewriteBase /
  # http://www.mysite.com/catalog/ = RewriteBase /catalog/ 
  # http://www.mysite.com/catalog/shop/ = RewriteBase /catalog/shop/
	RewriteBase / 
  # Change RewriteBase using the instructions above  
 	RewriteCond %{HTTP_REFERER} !^$
	RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?cadservices.co.uk [NC]
	RewriteCond %{HTTP_REFERER} !^.*google.* [NC]
	RewriteCond %{HTTP_REFERER} !^.*bing.* [NC]
	RewriteCond %{HTTP_REFERER} !^.*yahoo.* [NC]
	RewriteRule \.(jpg|jpeg|gif)$ https://www.cadservices.co.uk/images/autocadhl.png  [NC,R,L]  
  
	RewriteRule ^(.*)favicon\.(.*)$ favicon.ico [L]
	RewriteRule ^(.*)media/images(.*)$ $1/images/$2 [L]
  
	#if its a resource (add others that are missing)
	RewriteCond %{REQUEST_URI} \.(gif|css|png|js|jpe?g)$ [NC]
	#do nothing
	RewriteRule ^ - [L]

	RewriteRule ^(google)(.*).html$ - [L]
     
	RewriteRule ^(.*)-p-(.*).html$ product_info.php?products_id=$2&%{QUERY_STRING}
	RewriteRule ^(.*)-c-(.*).html$ index.php?cPath=$2&%{QUERY_STRING}
	RewriteRule ^(.*)-m-(.*).html$ index.php?manufacturers_id=$2&%{QUERY_STRING}	
	RewriteRule ^([^/]*)\.html$ /pages.php?page=$1 [L]  
	 
	RewriteCond %{HTTPS} off
	RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
	
	

</IfModule>

# Compress HTML, TXT, CSS, JavaScript, JSON, XML, HTC
<IfModule mod_deflate.c>
  <IfModule mod_setenvif.c>
    <IfModule mod_headers.c>
      SetEnvIfNoCase ^(Accept-EncodXng|X-cept-Encoding|X{15}|~{15}|-{15})$ ^((gzip|deflate)\s*,?\s*)+|[X~-]{4,13}$ HAVE_Accept-Encoding
      RequestHeader append Accept-Encoding "gzip,deflate" env=HAVE_Accept-Encoding
    </IfModule>
  </IfModule>
 <IfModule filter_module>
    FilterDeclare  COMPRESS
    FilterProvider COMPRESS DEFLATE "%{CONTENT_TYPE} =~ m#^text/(html|css|plain|xml|x-component)#i"
    FilterProvider COMPRESS DEFLATE "%{CONTENT_TYPE} =~ m#^application/(javascript|json|xml|xhtml+xml|rss+xml|atom+xml|vnd.ms-fontobject|x-font-ttf)#i"
    FilterProvider COMPRESS DEFLATE "%{CONTENT_TYPE} =~ m#^image/(svg+xml|x-icon)#i"
    FilterProvider COMPRESS DEFLATE "%{CONTENT_TYPE} = 'font/opentype'"
    FilterChain    COMPRESS
    FilterProtocol COMPRESS DEFLATE change=yes;byteranges=no
 </IfModule>
</IfModule>
### End - GZip compression of resources
<IfModule mod_expires.c>
        ExpiresActive On
        ExpiresDefault "access plus 1 day"
        ExpiresByType image/jpg "access plus 1 month"
        ExpiresByType image/jpeg "access plus 1 month"
        ExpiresByType image/gif "access plus 1 month"
        ExpiresByType image/png "access plus 1 month"
        ExpiresByType text/css "access plus 1 month"
        ExpiresByType application/pdf "access plus 1 month"
	ExpiresByType text/x-javascript "access plus 1 month"  
	ExpiresByType application/javascript "access plus 1 month"  
	ExpiresByType application/x-javascript "access plus 1 month"
        ExpiresByType image/x-icon "access plus 1 year"
</IfModule>