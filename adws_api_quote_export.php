<?php
use autodesk_api\AutodeskAPI;
if (!isset($path['fs_app_root'])) $path['fs_app_root'] = __DIR__ . "/baffletrain/autocadlt/autobooks/";
require_once $path['fs_app_root'] . "resources/system/startup_sequence.php";

// Set up the log file path

$csv_file_path = __DIR__ . '/feeds/quotes.csv';
// Connect to the test database (add your connection code here)
tcs_log("Connecting to the test database...",'quote_import');

// Get headers
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}
tcs_log("Headers retrieved: " . json_encode($headers),'quote_import');
// Get the raw POST data
$raw_post_data = file_get_contents('php://input');
tcs_log("Raw POST data received.",'quote_import');

// Decode the JSON payload
$json_payload = json_decode($raw_post_data, true);
AutodeskAPI::log_message("JSON payload decoded: " . json_encode($json_payload),'quote_import');

header('Content-type: application/json');
if (!empty($json_payload['result']['downloadLink'])) {
    if (is_string($json_payload['result']['downloadLink'])) {
       tcs_log("Download link found: " . $json_payload['result']['downloadLink']);
        try {
            $autodesk = new AutodeskAPI();
            tcs_log("AutodeskAPI instance created.",'quote_import');
            $download = false;      
           tcs_log("looking for file: " . $csv_file_path );      
            if (file_exists($csv_file_path)) {
                $file_age = time() - filemtime($csv_file_path);
               tcs_log("file found: " . $csv_file_path . " - age: " . $file_age);
                if ($file_age > 86400) {
                    $download = true;                    
                }else{
                   tcs_log("using cached file",'quote_import');
                }
            } else {
                $download = true;
            }
            tcs_log("Download: " . $download ? "true" : "false",'quote_import');
            if ($download) {
                $returned = $autodesk->quotes->download_autodesk_quote_export($json_payload['result']['downloadLink']);
                tcs_log(AutodeskAPI::log_message("Download initiated successfully: {$returned['status']} - {$returned['response']}" );
            }
            tcs_log("importing",'quote_import');
            $import = AutodeskAPI::import_csv_into_database(
                mapping: $autodesk->quotes->get_quote_column_mapping(),
                csv_file_path: $csv_file_path,
                debug: true);
           tcs_log("Import initiated successfully: {$import['status']}",'quote_import');
           tcs_log($import['response'],'quote_import');
        } catch (Exception $e) {
           tcs_log("Error initiating download: " . $e->getMessage(),'quote_import');
        }
    } else {
        tcs_log("Invalid download link format.",'quote_import');
    }
} else {
    tcs_log("No download link provided in payload.",'quote_import');
}
?>