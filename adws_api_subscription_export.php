<?php
use autodesk_api\autodesk_api;
use autodesk_api\autodesk_subscription;

if (!isset($path['fs_app_root'])) $path['fs_app_root'] = __DIR__ . "/baffletrain/autocadlt/autobooks";
require_once $path['fs_app_root'] . "/system/startup_sequence_minimal.php";



$csv_file_path = __DIR__ . '/feeds/subscriptions.csv';
// Connect to the test database (add your connection code here)
tcs_log("Connecting to the test database...","subscription_import");

// Get headers
$headers = [];
foreach ($_SERVER as $name => $value) {
    if (substr($name, 0, 5) == 'HTTP_') {
        $headers[str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))))] = $value;
    }
}
tcs_log("Headers retrieved: " . json_encode($headers),"subscription_import");

// Get the raw POST data
$raw_post_data = file_get_contents('php://input');
tcs_log("Raw POST data received.","subscription_import");

// Decode the JSON payload
$json_payload = json_decode($raw_post_data, true);
tcs_log("JSON payload decoded: " . json_encode($json_payload),"subscription_import");

header('Content-type: application/json');
if (!empty($json_payload['result']['downloadLink'])) {
    if (is_string($json_payload['result']['downloadLink'])) {
       tcs_log("Download link found: " . $json_payload['result']['downloadLink'],"subscription_import");
        try {
            $autodesk = new autodesk_api();
            tcs_log("autodesk_api instance created.");
            $download = false;      
            tcs_log("looking for file: " . $csv_file_path );      
            if (file_exists($csv_file_path)) {
                $file_age = time() - filemtime($csv_file_path);
               tcs_log("file found: " . $csv_file_path . " - age: " . $file_age,"subscription_import");
                if ($file_age > 86400) {
                    $download = true;                    
                }else{
                    tcs_log("using cached file" ); 
                }
            } else {
                $download = true;
            }
            tcs_log("Download: " . $download ? "true" : "false","subscription_import");
            if ($download) {
                $returned = $autodesk->subscriptions->download_autodesk_subscription_export($json_payload['result']['downloadLink']);
                tcs_log("Download initiated successfully: {$returned['status']} - {$returned['response']}" ,"subscription_import");
            }
            tcs_log("importing","subscription_import");
            $import = autodesk_api::import_csv_into_database(
                mapping: autodesk_subscription::$subscription_column_mapping,
                csv_file_path: $csv_file_path,
                debug: true);
            tcs_log("Import initiated successfully: {$import['status']}" ,"subscription_import");
            tcs_log($import['response'],"subscription_import");
        } catch (Exception $e) {
            tcs_log("Error initiating download: " . $e->getMessage(),"subscription_import");
        }
    } else {
        tcs_log("Invalid download link format.","subscription_import");
    }
} else {
    tcs_log("No download link provided in payload.","subscription_import");
}
?>