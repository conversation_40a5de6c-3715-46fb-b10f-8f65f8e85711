<?php
/**
 * Database Export Script for Local Development
 * 
 * SECURITY WARNING: Remove this file after use!
 * This script should only be used temporarily for database migration.
 */

// Prevent direct access from web (comment out for web access)
if (php_sapi_name() !== 'cli') {
    // Uncomment the line below to allow web access (REMOVE AFTER USE!)
    // die('This script can only be run from command line for security reasons.');
}

// Include your application's database configuration
require_once 'includes/configure.php';

// Database connection settings from your config
$host = DB_SERVER;
$username = DB_SERVER_USERNAME;
$password = DB_SERVER_PASSWORD;
$database = DB_DATABASE;

// Output file
$output_file = 'database_export_' . date('Y-m-d_H-i-s') . '.sql';

echo "Database Export Script\n";
echo "=====================\n";
echo "Host: $host\n";
echo "Database: $database\n";
echo "Output: $output_file\n\n";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Start building SQL export
    $sql_export = "-- Database Export for Local Development\n";
    $sql_export .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
    $sql_export .= "-- Database: $database\n\n";
    
    $sql_export .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $sql_export .= "START TRANSACTION;\n";
    $sql_export .= "SET time_zone = \"+00:00\";\n\n";
    
    // Get all tables
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Found " . count($tables) . " tables.\n";
    
    foreach ($tables as $table) {
        echo "Exporting table: $table\n";
        
        // Get table structure
        $create_table = $pdo->query("SHOW CREATE TABLE `$table`")->fetch();
        $sql_export .= "-- Table structure for table `$table`\n";
        $sql_export .= "DROP TABLE IF EXISTS `$table`;\n";
        $sql_export .= $create_table['Create Table'] . ";\n\n";
        
        // Get table data
        $rows = $pdo->query("SELECT * FROM `$table`");
        $row_count = $rows->rowCount();
        
        if ($row_count > 0) {
            $sql_export .= "-- Dumping data for table `$table`\n";
            $sql_export .= "INSERT INTO `$table` VALUES\n";
            
            $first_row = true;
            while ($row = $rows->fetch(PDO::FETCH_ASSOC)) {
                if (!$first_row) {
                    $sql_export .= ",\n";
                }
                
                $values = array();
                foreach ($row as $value) {
                    if ($value === null) {
                        $values[] = 'NULL';
                    } else {
                        $values[] = "'" . addslashes($value) . "'";
                    }
                }
                
                $sql_export .= "(" . implode(", ", $values) . ")";
                $first_row = false;
            }
            $sql_export .= ";\n\n";
        }
    }
    
    $sql_export .= "COMMIT;\n";
    
    // Write to file
    file_put_contents($output_file, $sql_export);
    
    echo "\nExport completed successfully!\n";
    echo "File saved as: $output_file\n";
    echo "File size: " . formatBytes(filesize($output_file)) . "\n\n";
    
    echo "Next steps:\n";
    echo "1. Download the file: $output_file\n";
    echo "2. Import it into your local database using phpMyAdmin or MySQL command line\n";
    echo "3. DELETE this export file from your server for security!\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

echo "\n=== SECURITY REMINDER ===\n";
echo "IMPORTANT: Delete this script file after use!\n";
echo "It contains database access code and should not remain on your server.\n";
?>
