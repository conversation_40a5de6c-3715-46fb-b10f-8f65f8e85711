# Database Migration Guide: Web Server to Local Development

This guide provides multiple methods to transfer your database from your web server to your local XAMPP environment.

## Method 1: phpMyAdmin Export/Import (Recommended)

### Step 1: Export from Web Server

1. **Access phpMyAdmin** on your web server
2. **Login** with your database credentials
3. **Select your database** (check `includes/configure.php` for the exact name)
4. **Click "Export" tab**
5. **Choose "Custom" export method**
6. **Configure export settings:**
   - **Format:** SQL
   - **Tables:** Select all tables (or specific ones you need)
   - **Output:** Save output to a file
   - **Format-specific options:**
     - ✅ Add DROP TABLE/VIEW/PROCEDURE/FUNCTION/EVENT/TRIGGER statement
     - ✅ Add CREATE PROCEDURE/FUNCTION/EVENT (if applicable)
     - ✅ Add IF NOT EXISTS (prevent errors on re-import)
   - **Data:**
     - ✅ Structure and data
     - ✅ Complete inserts
     - ✅ Extended inserts (for better performance)
7. **Click "Go"** to download the SQL file

### Step 2: Import to Local XAMPP

1. **Start XAMPP** services (Apache + MySQL)
2. **Open phpMyAdmin:** `http://localhost/phpmyadmin`
3. **Create database:** Click "New" → Enter `cadservices_local` → Click "Create"
4. **Select the database:** Click on `cadservices_local`
5. **Click "Import" tab**
6. **Choose file:** Browse and select your downloaded SQL file
7. **Configure import settings:**
   - **Format:** SQL
   - **Character set:** utf8 (or utf8mb4 if your export used it)
8. **Click "Go"** to import

## Method 2: Using Custom PHP Export Script

### Step 1: Upload Export Script to Web Server

1. **Upload** `database_export.php` to your web server root directory
2. **Access via web browser:** `https://yourdomain.com/database_export.php`
   - Or run via SSH: `php database_export.php`
3. **Download** the generated SQL file
4. **Delete** `database_export.php` from your server immediately after use!

### Step 2: Import Locally Using PHP Script

```batch
cd E:\Build\httpdocs
php database_import_local.php your_exported_file.sql
```

## Method 3: Command Line mysqldump (For SSH Access)

### If you have SSH access to your web server:

```bash
# On web server
mysqldump -u [username] -p [database_name] > database_backup.sql

# Download the file to your local machine
# Then import locally:
```

```batch
# On local machine
cd E:\tools\xampp\mysql\bin
mysql -u root -p cadservices_local < path\to\database_backup.sql
```

## Method 4: Using MySQL Workbench (GUI Tool)

1. **Download MySQL Workbench** (free from Oracle)
2. **Connect to your web server** database
3. **Use Data Export wizard** to export your database
4. **Connect to local XAMPP** MySQL
5. **Use Data Import wizard** to import

## Important Considerations

### Before Migration:

1. **Backup your local database** (if you have existing data)
2. **Check database size** - large databases may timeout in phpMyAdmin
3. **Note your current database name** from `includes/configure.php`
4. **Ensure XAMPP MySQL is running**

### After Migration:

1. **Update local configuration:**
   ```php
   // In includes/configure.php or use includes/configure_local.php
   define('DB_SERVER', '127.0.0.1');
   define('DB_SERVER_USERNAME', 'root');
   define('DB_SERVER_PASSWORD', '');
   define('DB_DATABASE', 'cadservices_local');
   ```

2. **Test the connection:**
   - Visit: `https://localhost.cadservices/php_config_check.php`
   - Check database connection status

3. **Clear any cached data:**
   - Clear `temp/` directory contents
   - Clear any session files

### Troubleshooting:

**Import fails with "MySQL server has gone away":**
- Increase `max_allowed_packet` in MySQL configuration
- Split large SQL files into smaller chunks
- Use command line import instead of phpMyAdmin

**Character encoding issues:**
- Ensure both export and import use the same charset (utf8mb4 recommended)
- Check your database tables' collation

**Permission errors:**
- Ensure MySQL user has proper privileges
- For XAMPP, 'root' user typically has all privileges

**Timeout errors:**
- Increase PHP `max_execution_time`
- Use command line tools for large databases
- Import in smaller batches

## Security Reminders

1. **Remove export scripts** from your web server after use
2. **Don't commit database files** to version control
3. **Use different passwords** for local development
4. **Regularly backup** your local development database

## File Sizes and Performance

- **Small databases (< 10MB):** phpMyAdmin works fine
- **Medium databases (10MB - 100MB):** Consider command line tools
- **Large databases (> 100MB):** Definitely use command line or split into chunks

## Quick Reference Commands

```batch
# Create local database
mysql -u root -p -e "CREATE DATABASE cadservices_local;"

# Import SQL file
mysql -u root -p cadservices_local < database_export.sql

# Check import success
mysql -u root -p -e "USE cadservices_local; SHOW TABLES;"
```

Choose the method that best fits your access level and database size. Method 1 (phpMyAdmin) is recommended for most users as it's the most straightforward and doesn't require command line access.
