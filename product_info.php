<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

  if (!isset($_GET['products_id'])) {
    tep_redirect(tep_href_link('index.php'));
  }

  require('includes/languages/' . $language . '/product_info.php');

  $product_check_query = tep_db_query("select count(*) as total from products p, products_description pd where p.products_status = '1' and p.products_id = '" . (int)$_GET['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
  $product_check = tep_db_fetch_array($product_check_query);

  require('includes/template_top.php');

  if ($product_check['total'] < 1) {
?>

	<div class="contentContainer">
		<div class="row">
			<?php echo $oscTemplate->getContent('product_info_not_found'); ?>
		</div>    
	</div>

<?php
  } else {
     $product_info_query_sql = "
    SELECT
    p.products_id,
    pd.products_name,
    pd.products_description,
    IF(p2c.products_to_categories_attribs_id, p2ca.products_model, p.products_model) AS products_model,
    p.products_quantity,
    p.products_image,
    pd.products_url,
    if (pa.products_id, pa.options_values_price + p.products_price, p.products_price) as products_price, 
    p.products_tax_class_id,
    p.products_date_added,
    p.products_date_available,
    p.manufacturers_id,
    p.products_gtin
FROM
    products p left join products_attributes pa on p.products_id = pa.products_id and pa.attribute_default = 1
    JOIN products_description pd ON pd.products_id = p.products_id
    JOIN products_to_categories p2c ON p2c.products_id = p.products_id
    LEFT JOIN products_to_categories_attrib p2ca on p2c.products_to_categories_attribs_id = p2ca.products_to_categories_attribs_id
					 
WHERE
    p.products_status = '1'
    AND p.products_id = '" . (int)$_GET['products_id'] . "'
    AND pd.language_id = '" . (int)$languages_id . "'
    AND p2c.categories_id = '" . (int)$current_category_id . "'";
    
    //echo $_GET['products_id'];
    
function parseStringForAttribs($string) {
    $pattern = '/(\d+)\{(\d+)\}(\d+)/';
    preg_match_all($pattern, $string, $matches, PREG_SET_ORDER);

    $parsedData = [];
    foreach ($matches as $match) {
        $productID = $match[1];
        $attributeID = $match[2];
        $valueID = $match[3];
        $parsedData[] = [
            'productID' => $productID,
            'attributeID' => $attributeID,
            'valueID' => $valueID,
        ];
    }

    return $parsedData;
}

    
    $product_info_query = tep_db_query($product_info_query_sql);
   
    //$product_info_query = tep_db_query("select p.products_id, pd.products_name, pd.products_description, p.products_model, p.products_quantity, p.products_image, pd.products_url, p.products_price, p.products_tax_class_id, p.products_date_added, p.products_date_available, p.manufacturers_id, p.products_gtin from products p, products_description pd where p.products_status = '1' and p.products_id = '" . (int)$_GET['products_id'] . "' and pd.products_id = p.products_id and pd.language_id = '" . (int)$languages_id . "'");
    $product_info = tep_db_fetch_array($product_info_query);
    tep_db_query("update products_description set products_viewed = products_viewed+1 where products_id = '" . (int)$_GET['products_id'] . "' and language_id = '" . (int)$languages_id . "'");

/* 
	Testing...
	$string1 = "10553{2}4{1}1";
	$string2 = "10555{72}4{1}10{1}10";
	$string3 = "10551{72}4";

	$parsedData1 = parseString($string1);
	$parsedData2 = parseString($string2);
	$parsedData3 = parseString($string3);

	?><br><br><?php
	print_r($parsedData1);
	?><br><br><?php
	print_r($parsedData2);

	?><br><br><?php
	print_r($parsedData3);
*/

?>

<?php echo tep_draw_form('cart_quantity', tep_href_link('product_info.php', tep_get_all_get_params(array('action')). 'action=add_product', 'NONSSL'), 'post', 'class="form-horizontal"  id="addCartForm" role="form"'); ?>

<?php
  if ($messageStack->size('product_action') > 0) {
    echo $messageStack->output('product_action');
  }
?>

<div class="contentContainer">

  <div class="row is-product">
    <?php echo $oscTemplate->getContent('product_info'); ?>
  </div>

</div>

</form>

<?php
  }
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
