<?php
/**
 * Google XML Sitemap Feed Cron Script
 *
 * The Google sitemap service was announced on 2 June 2005 and represents
 * a huge development in terms of crawler technology.  This contribution is
 * designed to create the sitemap XML feed per the specification delineated 
 * by Google.  This cron script will call the code to create the scripts and 
 * eliminate the session auto start issues. 
 * @package Google-XML-Sitemap-Feed
 * @license http://opensource.org/licenses/gpl-license.php GNU Public License
 * @version 1.11
 * @link http://www.oscommerce-freelancers.com/ osCommerce-Freelancers
 * @link http://www.google.com/webmasters/sitemaps/docs/en/about.html About Google Sitemap 
 * @copyright Copyright 2005, <PERSON> 
 * <AUTHOR> 
 * @filesource
 */

	chdir('../');
 
 error_reporting(0);
 ini_set('display_errors','0'); 

	/**
	 * Option to compress the files
	 */
	define('GOOGLE_SITEMAP_COMPRESS', 'false');
	define('GOOGLE_SITEMAP_PROD_CHANGE_FREQ', 'weekly');
	define('GOOGLE_SITEMAP_CAT_CHANGE_FREQ', 'weekly');
	define('GOOGLE_SITEMAP_IMAGES_CHANGE_FREQ', 'weekly');
	define('GOOGLE_SITEMAP_MAN_CHANGE_FREQ', 'weekly');
	define('GOOGLE_SITEMAP_SPECIALS_CHANGE_FREQ', 'weekly');
	define('GOOGLE_SITEMAP_PAGES_CHANGE_FREQ', 'weekly'); 
 define('GOOGLE_SITEMAP_TOPICS_CHANGE_FREQ', 'weekly');
	
 define('DIR_WS_CATALOG', DIR_WS_HTTP_CATALOG);

	require_once('includes/configure.php');
	require_once('includes/database_tables.php');
	require_once('includes/functions/database.php');
	tep_db_connect() or die('Unable to connect to database server!');

	$configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from configuration');
	while ($configuration = tep_db_fetch_array($configuration_query)) {
		define($configuration['cfgKey'], $configuration['cfgValue']);
	}

	function @tep_not_null($value) {
		if (is_array($value)) {
		  if (sizeof($value) > 0) {
			return true;
		  } else {
			return false;
		  }
		} else {
		  if (($value != '') && (strtolower($value) != 'null') && (strlen(trim($value)) > 0)) {
			return true;
		  } else {
			return false;
		  }
		}
	} # end function

	include_once('includes/classes/language.php');
	$lng = new language();
	$languages_id = $lng->language['id'];
 
  
if ( defined('SEO_URLS') && SEO_URLS == 'true' || defined('SEO_ENABLED') && SEO_ENABLED == 'true' ) {

	function tep_session_is_registered( $var ){
		return false;
	}  # end function

	function tep_session_name(){
		return false;
	} # end function
	
	function tep_session_id(){
		return false;
	} # end function

	function tep_parse_input_field_data($data, $parse) {
		return strtr(trim($data), $parse);
	} # end function

	function tep_output_string($string, $translate = false, $protected = false) {
		if ($protected == true) {
		  return htmlspecialchars($string);
		} else {
		  if ($translate == false) {
	    		return tep_parse_input_field_data($string, array('"' => '&quot;'));
		  } else {
	    		return tep_parse_input_field_data($string, $translate);
		  }
		}
	} # end function

	
	if ( file_exists('includes/classes/seo.class.php') ){
    if (! defined("tep_get_parent_categories"))
    {
      function tep_get_parent_categories(&$categories, $categories_id) {
        $parent_categories_query = tep_db_query("select parent_id from categories where categories_id = '" . (int)$categories_id . "'");
        while ($parent_categories = tep_db_fetch_array($parent_categories_query)) {
          if ($parent_categories['parent_id'] == 0) return true;
          $categories[sizeof($categories)] = $parent_categories['parent_id'];
          if ($parent_categories['parent_id'] != $categories_id) {
            tep_get_parent_categories($categories, $parent_categories['parent_id']);
          }
        }
      }
    }
    require_once('includes/classes/seo.class.php');
    $seo_urls = new SEO_URL($languages_id);
	}

	require_once('includes/functions/html_output.php');
	
	if ( file_exists('includes/classes/cache.class.php') ){
		include('includes/classes/cache.class.php');
		$cache = new cache($languages_id);
		if ( file_exists('includes/seo_cache.php') ){
			include('includes/seo_cache.php');
		}
		$cache->get_cache('GLOBAL');
	}
} # end if


$mapfile = 'sitemap.class.php';
require_once('googlesitemap/' . $mapfile);

$file = file_get_contents('includes/application_top.php');
$version = "<head>";
if (strpos($file, 'v2.3') !== FALSE) {
  $version = "template_top.php";
}

$domainArray = array();
$domainArray[] = HTTP_SERVER . DIR_WS_HTTP_CATALOG;

if (@tep_not_null(GOOGLE_XML_SITEMAP_MOBILE_URL)) {
$domainArray[] = GOOGLE_XML_SITEMAP_MOBILE_URL;
}

$map_type = 'standard';
 
foreach ($domainArray as $domain_name) {
   $google = new GoogleSitemap(DB_SERVER, DB_SERVER_USERNAME, DB_DATABASE, DB_SERVER_PASSWORD, $version, $domain_name, $map_type);

   $showImages = false;
   $showManufacturers = false;
   $showPages = false;
   $showSpecials = false;
   $showTopics = false;
   $submit = true;

   echo '<pre>';
   if ($google->GenerateProductSitemap()){
    if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Product Sitemap Successfully' . "\n\n";
   } else {
    $submit = false;
    echo 'ERROR: Google Product Sitemap Generation FAILED!' . "\n\n";
   }

   if ($google->GenerateCategorySitemap()){
    if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Category Sitemap Successfully' . "\n\n";
   } else {
    $submit = false;
    echo 'ERROR: Google Category Sitemap Generation FAILED!' . "\n\n";
   }

   if (GOOGLE_XML_SITEMAP_CREATE_IMAGES == 'true') {
     if ($google->GenerateImagesSitemap()){
         $showImages = true;
         if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Images Sitemap Successfully' . "\n\n";
     } else {
         $submit = false;
         echo 'ERROR: Google Images Sitemap Generation FAILED!' . "\n\n";
     }
   }
    
   if (GOOGLE_XML_SITEMAP_CREATE_MANU == 'true') {
     if ($google->GenerateManufacturerSitemap()){
           $showManufacturers = true;
      if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Manufacturers Sitemap Successfully' . "\n\n";
     } else {
       $manufacturers_query = tep_db_query("select manufacturers_id from manufacturers limit 1");
       if (tep_db_num_rows($manufacturers_query) > 0) {
           $submit = false;
           echo 'ERROR: Google Manufacturers Sitemap Generation FAILED!' . "\n\n";
       } else {
           echo 'Google Sitemap Manufacturers not generated - no Manufacturers found!' . "\n\n";
       }
     }
   }
   if (GOOGLE_XML_SITEMAP_CREATE_PAGES == 'true') {
     if ($google->GeneratePagesSitemap()){
         $showPages = true;
         if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Pages Sitemap Successfully' . "\n\n";
     } else {
         $submit = false;
         echo 'ERROR: Google Pages Sitemap Generation FAILED!' . "\n\n";
     }
   }

   if (GOOGLE_XML_SITEMAP_CREATE_SPECIALS == 'true') {
     if ($google->GenerateSpecialsSitemap($languages_id)){
           $showSpecials = true;
      if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Specials Sitemap Successfully' . "\n\n";
     } else {
       $specials_query = tep_db_query("select p.products_id from products p, products_description pd, specials s where p.products_status = '1' and p.products_id = s.products_id and pd.products_id = s.products_id and pd.language_id = '" . (int)$languages_id . "' and s.status = '1' limit 1");
       if (tep_db_num_rows($specials_query) > 0) {
        $submit = false;
        echo 'ERROR: Google Specials Sitemap Generation FAILED!' . "\n\n";
       } else {
        echo 'Google Sitemap Specials not generated - no specials found!' . "\n\n";
       }
     }
   }

   if (GOOGLE_XML_SITEMAP_CREATE_TOPICS == 'true') {
     if ($google->GenerateTopicsSitemap($languages_id)){
           $showTopics = true;
      if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Topics Sitemap Successfully' . "\n\n";
     } else {
      $submit = false;
      echo 'ERROR: Google Topics Sitemap Generation FAILED!' . "\n\n";
     }
   }

   if ($google->GenerateSitemapIndex()){
    if (GOOGLE_XML_SITEMAP_SHOW_DIAGNOSTIC == 'true') echo 'Generated Google Sitemap Index Successfully' . "\n\n";
   } else {
    $submit = false;
    echo 'ERROR: Google Sitemap Index Generation FAILED!' . "\n\n";
   }

   if ($submit){
      if ($map_type == 'standard') { //only show once
          echo 'CONGRATULATIONS! All files generated successfully.' . "\n\n";
          echo 'If you have not already submitted the sitemap index to Google or Bing click the link(s) below.' . "\n";
          echo 'Before doing that, please be sure to view the XML files to make sure the data is correct.' . "\n\n";
          echo "\t" . '<a href="' . $google->GenerateSubmitURL('google') . '">Notify Google</a>';
          echo "\t" . '<a href="' . $google->GenerateSubmitURL('bing') . '">Notify Bing</a>' . "\n\n";
          echo str_repeat("-=", 60) ."\n";
          
          echo 'For your convenience here is the CRON command for your site:' . "\n\n";
          echo "\t" . 'php ' . dirname($_SERVER['SCRIPT_FILENAME']) . '/index.php' . "\n\n";
          echo str_repeat("-=", 60) ."\n";
          
          echo 'The following should be added to your Google and Bing Webmaster Tools accounts.' . "\n";
          echo 'They only require the index to be submitted but listing all of them will make it easier to troubleshoot problems.' . "\n\n";
          echo "\t" . 'sitemap: ' . $google->base_url . 'sitemapindex.xml' . "\n";
          if (count($domainArray) > 1) {
             echo "\t" . 'sitemap: ' . $google->base_url . 'sitemap_mobileindex.xml' . "\n";
          }
          echo "\n";
          echo str_repeat("-=", 60) ."\n";
          
          echo 'If you have a robots.txt file (located in the root directory of your site), add this to the end of it:' . "\n\n";
          echo "\t" . 'sitemap: ' . $google->base_url . 'sitemapindex.xml' . "\n";
          if (count($domainArray) > 1) {
             echo "\t" . 'sitemap: ' . $google->base_url . 'sitemap_mobileindex.xml' . "\n";
          }
          echo "\n";
      }
      echo str_repeat("-=", 60) ."\n";
                     
      echo 'Here are the map files for the URL '. $google->base_url . "\n\n";     
      echo ($map_type == 'standard' ? 'sitemapindex.xml' : 'sitemap_mobileindex.xml') . "\n";
      echo ($map_type == 'standard' ? 'sitemapproducts.xml' : 'sitemap_mobileproducts.xml') . "\n";
      echo ($map_type == 'standard' ? 'sitemapcategories.xml' : 'sitemap_mobilecategories.xml') . "\n";

      if (GOOGLE_XML_SITEMAP_CREATE_IMAGES == 'true' && $showImages)
       echo ($map_type == 'standard' ? 'sitemapimages.xml' : 'sitemap_mobileimages.xml') . "\n";
      
      if (GOOGLE_XML_SITEMAP_CREATE_MANU == 'true' && $showManufacturers)
       echo ($map_type == 'standard' ? 'sitemapmanufacturers.xml' : 'sitemap_mobilemanufacturers.xml') . "\n";

      if (GOOGLE_XML_SITEMAP_CREATE_PAGES == 'true')
       echo ($map_type == 'standard' ? 'sitemappages.xml' : 'sitemap_mobilepages.xml') . "\n";

      if (GOOGLE_XML_SITEMAP_CREATE_SPECIALS == 'true' && $showSpecials)
       echo ($map_type == 'standard' ? 'sitemapspecials.xml' : 'sitemap_mobilespecials.xml') . "\n";

      if (GOOGLE_XML_SITEMAP_CREATE_TOPICS == 'true' && $showTopics)
       echo ($map_type == 'standard' ? 'sitemaptopics.xml' : 'sitemap_mobiletopics.xml') . "\n";
   } else {
    print_r($google->debug);
   }

   echo '</pre>';

   if (count($domainArray) > 1) {
       $map_type = 'mobile'; //for the next pass
       define ('MOBILE_SESSION_GOOGLE', true); //trick Ultimate SEO into using the mobile url
   }
}

echo 'Completed!';
?>