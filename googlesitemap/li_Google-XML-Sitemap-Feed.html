<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Google XML Sitemap Feed Documentation</title>
<link rel="stylesheet" type="text/css" href="media/style.css">
<meta http-equiv='Content-Type' content='text/html; charset=iso-8859-1'/>
</head>
<body>
<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
    <tr>
        <td class="header-top-left"><img src="media/logo.png" border="0" alt="phpDocumentor " /></td>
        <td class="header-top-right">Google-XML-Sitemap-Feed<br />
            <div class="header-top-right-subpackage"></div></td>
    </tr>
    <tr>
        <td colspan="2" class="header-line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td>
    </tr>
    <tr>
        <td colspan="2" class="header-menu"> [ <a href="classtrees_Google-XML-Sitemap-Feed.html" class="menu">class tree: Google-XML-Sitemap-Feed</a> ] [ <a href="elementindex_Google-XML-Sitemap-Feed.html" class="menu">index: Google-XML-Sitemap-Feed</a> ] [ <a href="elementindex.html" class="menu">all elements</a> ] </td>
    </tr>
    <tr>
        <td colspan="2" class="header-line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td>
    </tr>
</table>
<table width="100%" border="0" cellpadding="0" cellspacing="0">
    <tr valign="top">
        <td width="195" class="menu"><div class="package-title">Google-XML-Sitemap-Feed</div>
            <b>Packages:</b><br />
            <div class="package"> <a href="li_Google-XML-Sitemap-Feed.html">Google-XML-Sitemap-Feed</a><br />
            </div>
            <br />
            <b>Files:</b><br />
            <div class="package"> <span style="padding-left: 1em;"><a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html">index.php</a></span><br />
                <span style="padding-left: 1em;"><a href="Google-XML-Sitemap-Feed/_googlesitemap_sitemap_class_php.html">sitemap.class.php</a></span><br />
            </div>
            <br />
            <b>Classes:</b><br />
            <div class="package"> <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html">GoogleSitemap</a> <br />
                <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html">MySQL_DataBase</a> <br />
            </div></td>
        <td><table cellpadding="10" cellspacing="0" width="100%" border="0">
                <tr>
                    <td valign="top"><div align="center">
                            <h1>Google XML Sitemap Feed Documentation</h1>
                        </div>
                        <p><strong>Welcome to the Google XML Sitemap Feed Documentation - by <a href="http://forums.oscommerce.com/index.php?showuser=9196" target="_blank">Chemo</a></strong></p>
                        <p>This contribution was coded to meet the <a href="http://www.google.com/webmasters/sitemaps/docs/en/protocol.html" target="_blank">protocol specification</a> delineated by Google for the <a href="http://www.google.com/webmasters/sitemaps/docs/en/about.html" target="_blank">Sitemaps experimental service</a>.</p>
                        <fieldset>
                        <legend>Current features:</legend>
                        <ul>
                            <li>Super easy install - no file edits! Just upload the files, set the permissions, and optionally create a CRON job to handle automatic maintenance. </li>
                            <li>Generation of sitemap files for products and categories (separate files)</li>
                            <li>Generation of a sitemap index file</li>
                            <li>Built-in support for large catalogs (more than 50,000 products, categories, or both)</li>
                            <li>Optional file compression per Google specification (GZ format)</li>
                            <li>Thorough documentation for developers! Don't be shy...improve the code if you can. </li>
                        </ul>
                        </fieldset>
                        <br/>
                        <fieldset>
                        <legend>Install Directions</legend>
                        <p>The contribution package should have the following structure - important files are bold:</p>
                        <ul>
                            <li>install.html (redirects to this documentation page)</li>
                            <li><b>sitemapproducts.xml</b> (dummy file)</li>
                            <li><b>sitemapcategories.xml</b> (dummy file)</li>
                            <li><b>sitemapindex.xml</b> (dummy file)</li>
                            <li><b>googlesitemap (directory)</b></li>
                            <ul>
                                <li><b>index.php</b></li>
                                <li><b>sitemap.class.php</b></li>
                                <li>...various documentation</li>
                            </ul>
                        </ul>
                        <p>Upload the <b>googlesitemap directory to your catalog directory</b>. If your store is installed in the domain root (domain.com/) then it should be accessible via browser like domain.com/googlesitemap/. As another example, if you store is installed in the &quot;catalog&quot; directory it will look like domain.com/catalog/googlesitemap/. Do not change the name of the directory!</p>
                        <p>Upload the dummy files (the XML files) to your <b>catalog directory</b>. So, when you are done you should be able to call the files in your browser like this:</p>
                        <b>If the store is in the document root: </b>
                        <ul>
                            <li>domain.com/sitemapproducts.xml</li>
                            <li>domain.com/sitemapcategories.xml</li>
                            <li>domain.com/sitemapindex.xml</li>
                        </ul>
                        <b>Else if the store is in a directory: </b>
                        <ul>
                            <li>domain.com/<b>directory</b>/sitemapproducts.xml</li>
                            <li>domain.com/<b>directory</b>/sitemapcategories.xml</li>
                            <li>domain.com<b>/directory</b>/sitemapindex.xml</li>
                        </ul>
                        <p>Once you have the dummy XML files uploaded you will need to make them wrtieable by the web server. The easiest way to do this is to start your favorite FTP client, right click the files, and change the permissions on each one. The correct settings will vary based on your server setup but generally speaking a setting of <b>777 or read, write, execute</b> will work every time.</p>
                        <p>At this point you have the googlesitemap directory and dummy files uploaded with proper permissions. Congratulations! You are done with the installation! Wasn't that easy? Now it's time to test the code...</p>
                        <p>To test the script simply call the /googlesitemap/<b>index.php</b> file in your browser. This is a special script that was designed to be called either via browser or CRON job and will generate the proper sitemap files for you. Before we setup the CRON to handle automatic maintenance we have to test it with the browser.</p>
                        <p>http://www.yourdomain.com/{<i>directory?</i>}/
                            <input name="textfield" type="text" value="googlesitemap/index.php" size="30" />
&lt;= cut-n-paste for your convenience :-) </p>
                        <p>Once you call the script in your browser the text may run together an appear pretty sloppy. However, keep in mind that the output is perfect for CRON jobs...in the browser it sucks. The <b>CONTENT is what matters</b>. If there are no errors such as wrong permission or other the near last line should say &quot;<b>If you have not already submitted the sitemap index to Google click the link below</b>&quot;. If you see this text <font color="#990000"><b>CONGRATULATIONS</b></font>...your contribution is installed and you are ready to move onto the CRON setup (optional...but if you're this far might as well give it a go). However, if you encountered errors please skip down to the quick help section below.</p>
                        <p>If the above browser test was successful you should now be able to view the XML files for quality assurance. </p>
                        <div>http://www.yourdomain.com/{<i>directory?</i>}/
                            <input name="textfield" type="text" value="sitemapindex.xml" size="30" />
                        </div>
                        <div>http://www.yourdomain.com/{<i>directory?</i>}/
                            <input name="textfield" type="text" value="sitemapproducts.xml" size="30" />
                        </div>
                        <div>http://www.yourdomain.com/{<i>directory?</i>}/
                            <input name="textfield" type="text" value="sitemapcategories.xml" size="30" />
                            <p>Once you have verified the data quality it's time to setup the CRON so the system is maintained automatically. I'll be using cPanel for this and if you use a different domainCP please ask your host for specific directions.</p>
                            <p>The first step is to login to your cPanel and find the CRON menu. (click for full size image)</p>
                            <p><a href="media/cron1.gif" target="_blank"><img src="media/cron1-thumb.gif" alt="" width="225" height="274" border="1" /></a> </p>
                            <p>The next step is to select the &quot;advanced&quot; menu. The reason is that everything is already setup as default and all you have to do is enter the command + path to index.php (click for full size image). </p>
                            <p><a href="media/cron2.gif" target="_blank"><img src="media/cron2-thumb.gif" width="225" height="69" border="1" /></a></p>
                            <p>Notice that the default CRON time setting is to execute <b>every day at midnight</b>. For most this is perfectly acceptable and makes it easier to create this task. The only thing left to do is enter the command.</p>
                            <p>CRON Command:
                                <input name="textfield" type="text" value="php /path/to/googlesitemap/index.php" size="50" />
&lt;= change the path! </p>
                            <p><b>Change the path to the correct location!</b> On Linux based servers it will ALWAYS start with a forward slash.</p>
                            <p>A nice feature of the CRON is that it will automatically email you the results of the task. I recommend receiving at least the first 1 as it has the submission link as part of the output! So, enter a valid email address in the field and wait for the email!</p>
                            <p>After you get the first email and click the submit link you are all done! The system will update itself every day at midnight and Google will use the sitemap files to crawl your site.</p>
                            <p>Enjoy! </p>
                        </div>
                        </fieldset>
                        <br/>
                        <fieldset>
                        <legend>Support</legend>
                        <a href="http://forums.oscommerce.com/index.php?showtopic=154082" target="_blank">Support thread</a>
                        </fieldset>
                        <br/>
                        <fieldset>
                        <legend>Donations</legend>
                        <p>This contribution was coded for the benefit of the community. To help support my coding efforts consider a donation...</p>
                        <div>
                            <form action="https://www.paypal.com/cgi-bin/webscr" method="post" target=_blank name=donate>
                                <input type="hidden" name="cmd" value="_xclick">
                                <input type="hidden" name="business" value="<EMAIL>">
                                <input type="hidden" name="item_name" value="Google Sitemap XML Feed">
                                <input type="hidden" name="item_number" value="1">
                                Enter Donation amount: $
                                <input type="text" name="amount" value="" size=6>
                                <input name="submit" type="image" src="media/img.gif" alt="Make payments with PayPal - it's fast, free and secure!" width="72" height="29" border="0">
                            </form>
                        </div>
                        </fieldset>
                        <div class="credit">
                            <hr class="separator" />
                            Documentation generated on Sat, 4 Jun 2005 23:45:56 -0400 by <a href="http://www.phpdoc.org">phpDocumentor 1.3.0RC3</a> </div></td>
                </tr>
            </table></td>
    </tr>
</table>
</body>
</html>
