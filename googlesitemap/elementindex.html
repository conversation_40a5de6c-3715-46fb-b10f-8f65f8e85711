<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
  <html xmlns="http://www.w3.org/1999/xhtml">
<head>
	<title>Element Index</title>
	<link rel="stylesheet" type="text/css" href="media/style.css">
	<meta http-equiv='Content-Type' content='text/html; charset=iso-8859-1'/>
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
	<td class="header-top-left"><img src="media/logo.png" border="0" alt="phpDocumentor " /></td>
    <td class="header-top-right">Google-XML-Sitemap-Feed<br /><div class="header-top-right-subpackage"></div></td>
  </tr>
  <tr><td colspan="2" class="header-line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td colspan="2" class="header-menu">
  		  [ <a href="classtrees_Google-XML-Sitemap-Feed.html" class="menu">class tree: Google-XML-Sitemap-Feed</a> ]
		  [ <a href="elementindex_Google-XML-Sitemap-Feed.html" class="menu">index: Google-XML-Sitemap-Feed</a> ]
		  [ <a href="elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td colspan="2" class="header-line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="195" class="menu">
		<div class="package-title">Google-XML-Sitemap-Feed</div>
      <b>Packages:</b><br />
  <div class="package">
              <a href="li_Google-XML-Sitemap-Feed.html">Google-XML-Sitemap-Feed</a><br />
      	</div>
      <br />
                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<a name="top"></a>
<h1>Index of All Elements</h1>
<h3>Package Indexes</h3>
<ul>
	<li><a href="elementindex_Google-XML-Sitemap-Feed.html">Google-XML-Sitemap-Feed</a></li>
</ul>
<br />
	[ <a href="elementindex.html#$">$</a> ]
	[ <a href="elementindex.html#b">b</a> ]
	[ <a href="elementindex.html#c">c</a> ]
	[ <a href="elementindex.html#d">d</a> ]
	[ <a href="elementindex.html#f">f</a> ]
	[ <a href="elementindex.html#g">g</a> ]
	[ <a href="elementindex.html#h">h</a> ]
	[ <a href="elementindex.html#i">i</a> ]
	[ <a href="elementindex.html#l">l</a> ]
	[ <a href="elementindex.html#m">m</a> ]
	[ <a href="elementindex.html#n">n</a> ]
	[ <a href="elementindex.html#p">p</a> ]
	[ <a href="elementindex.html#q">q</a> ]
	[ <a href="elementindex.html#r">r</a> ]
	[ <a href="elementindex.html#s">s</a> ]
	[ <a href="elementindex.html#u">u</a> ]
<br /><br />
	<a name="$"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">$</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>$</h2>
		<dl>
							<dt><b>$configuration['cfgKey']</b></dt>
				<dd>in file index.php, constant <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html#define$configuration['cfgKey']">$configuration['cfgKey']</a></dd>
					</dl>
	</div>
	<a name="b"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">b</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>b</h2>
		<dl>
							<dt><b>$base_url</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#var$base_url">GoogleSitemap::$base_url</a><br>&nbsp;&nbsp;&nbsp;&nbsp;$base_url is the URL for the catalog</dd>
					</dl>
	</div>
	<a name="c"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">c</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>c</h2>
		<dl>
							<dt><b>CompressFile</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodCompressFile">GoogleSitemap::CompressFile()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to compress a normal file</dd>
							<dt><b>ConnectDB</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodConnectDB">MySQL_DataBase::ConnectDB()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to connect to MySQL</dd>
					</dl>
	</div>
	<a name="d"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">d</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>d</h2>
		<dl>
							<dt><b>$db</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#var$db">MySQL_DataBase::$db</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Database name</dd>
							<dt><b>$DB</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#var$DB">GoogleSitemap::$DB</a><br>&nbsp;&nbsp;&nbsp;&nbsp;$DB is the database object</dd>
							<dt><b>DBPerform</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodDBPerform">MySQL_DataBase::DBPerform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to perform DB inserts and updates - abstracted from osCommerce-MS-2.2 project</dd>
							<dt><b>DIR_WS_CATALOG</b></dt>
				<dd>in file index.php, constant <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html#defineDIR_WS_CATALOG">DIR_WS_CATALOG</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Carried over from application_top.php for compatibility</dd>
					</dl>
	</div>
	<a name="f"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">f</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>f</h2>
		<dl>
							<dt><b>$filename</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#var$filename">GoogleSitemap::$filename</a><br>&nbsp;&nbsp;&nbsp;&nbsp;$filename is the base name of the feeds (i.e. - 'sitemap')</dd>
							<dt><b>FetchArray</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodFetchArray">MySQL_DataBase::FetchArray()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to fetch array</dd>
							<dt><b>Free</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodFree">MySQL_DataBase::Free()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to free the resource</dd>
					</dl>
	</div>
	<a name="g"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">g</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>g</h2>
		<dl>
							<dt><b>GenerateCategorySitemap</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGenerateCategorySitemap">GoogleSitemap::GenerateCategorySitemap()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Funciton to generate category sitemap data</dd>
							<dt><b>GenerateProductSitemap</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGenerateProductSitemap">GoogleSitemap::GenerateProductSitemap()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to generate product sitemap data</dd>
							<dt><b>GenerateSitemap</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGenerateSitemap">GoogleSitemap::GenerateSitemap()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to generate sitemap file from data</dd>
							<dt><b>GenerateSitemapIndex</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGenerateSitemapIndex">GoogleSitemap::GenerateSitemapIndex()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to generate sitemap index file</dd>
							<dt><b>GenerateSubmitURL</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGenerateSubmitURL">GoogleSitemap::GenerateSubmitURL()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Utility function to generate the submit URL</dd>
							<dt><b>GoogleSitemap</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodGoogleSitemap">GoogleSitemap::GoogleSitemap()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;GoogleSitemap class constructor</dd>
							<dt><b>GoogleSitemap</b></dt>
				<dd>in file sitemap.class.php, class <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html">GoogleSitemap</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Google Sitemap Base Class</dd>
							<dt><b>GOOGLE_SITEMAP_CAT_CHANGE_FREQ</b></dt>
				<dd>in file index.php, constant <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html#defineGOOGLE_SITEMAP_CAT_CHANGE_FREQ">GOOGLE_SITEMAP_CAT_CHANGE_FREQ</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Option for change frequency of categories</dd>
							<dt><b>GOOGLE_SITEMAP_COMPRESS</b></dt>
				<dd>in file index.php, constant <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html#defineGOOGLE_SITEMAP_COMPRESS">GOOGLE_SITEMAP_COMPRESS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Option to compress the files</dd>
							<dt><b>GOOGLE_SITEMAP_PROD_CHANGE_FREQ</b></dt>
				<dd>in file index.php, constant <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html#defineGOOGLE_SITEMAP_PROD_CHANGE_FREQ">GOOGLE_SITEMAP_PROD_CHANGE_FREQ</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Option for change frequency of products</dd>
					</dl>
	</div>
	<a name="h"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">h</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>h</h2>
		<dl>
							<dt><b>$host</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#var$host">MySQL_DataBase::$host</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Database host (localhost, IP based, etc)</dd>
					</dl>
	</div>
	<a name="i"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">i</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>i</h2>
		<dl>
							<dt><b>index.php</b></dt>
				<dd>procedural page <a href="Google-XML-Sitemap-Feed/_googlesitemap_index_php.html">index.php</a></dd>
					</dl>
	</div>
	<a name="l"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">l</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>l</h2>
		<dl>
							<dt><b>$link_id</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#var$link_id">MySQL_DataBase::$link_id</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Database link</dd>
					</dl>
	</div>
	<a name="m"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">m</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>m</h2>
		<dl>
							<dt><b>MySQL_DataBase</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodMySQL_DataBase">MySQL_DataBase::MySQL_DataBase()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;MySQL_DataBase class constructor</dd>
							<dt><b>MySQL_DataBase</b></dt>
				<dd>in file sitemap.class.php, class <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html">MySQL_DataBase</a><br>&nbsp;&nbsp;&nbsp;&nbsp;MySQL_Database Class</dd>
					</dl>
	</div>
	<a name="n"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">n</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>n</h2>
		<dl>
							<dt><b>NumRows</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodNumRows">MySQL_DataBase::NumRows()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to fetch the number of rows</dd>
					</dl>
	</div>
	<a name="p"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">p</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>p</h2>
		<dl>
							<dt><b>$pass</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#var$pass">MySQL_DataBase::$pass</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Database password</dd>
					</dl>
	</div>
	<a name="q"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">q</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>q</h2>
		<dl>
							<dt><b>Query</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodQuery">MySQL_DataBase::Query()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to perform queries</dd>
					</dl>
	</div>
	<a name="r"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">r</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>r</h2>
		<dl>
							<dt><b>ReadGZ</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodReadGZ">GoogleSitemap::ReadGZ()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Utility function to read and return the contents of a GZ formatted file</dd>
					</dl>
	</div>
	<a name="s"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">s</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>s</h2>
		<dl>
							<dt><b>$savepath</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#var$savepath">GoogleSitemap::$savepath</a><br>&nbsp;&nbsp;&nbsp;&nbsp;$savepath is the path where the feeds will be saved - store root</dd>
							<dt><b>sitemap.class.php</b></dt>
				<dd>procedural page <a href="Google-XML-Sitemap-Feed/_googlesitemap_sitemap_class_php.html">sitemap.class.php</a></dd>
							<dt><b>SaveFile</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/GoogleSitemap.html#methodSaveFile">GoogleSitemap::SaveFile()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to save the sitemap data to file as either XML or XML.GZ format</dd>
							<dt><b>SelectDB</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodSelectDB">MySQL_DataBase::SelectDB()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to select the database</dd>
							<dt><b>Slashes</b></dt>
				<dd>in file sitemap.class.php, method <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#methodSlashes">MySQL_DataBase::Slashes()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Function to add slashes</dd>
					</dl>
	</div>
	<a name="u"></a>
	<div class="index-letter-section">
		<div style="float: left" class="index-letter-title">u</div>
		<div style="float: right"><a href="#top">[Top]</a></div>
		<div style="clear: both"></div>
	</div>
	<div>
		<h2>u</h2>
		<dl>
							<dt><b>$user</b></dt>
				<dd>in file sitemap.class.php, variable <a href="Google-XML-Sitemap-Feed/MySQL_DataBase.html#var$user">MySQL_DataBase::$user</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Database user</dd>
					</dl>
	</div>
        <div class="credit">
		    <hr class="separator" />
		    Documentation generated on Sat,  4 Jun 2005 23:45:56 -0400 by <a href="http://www.phpdoc.org">phpDocumentor 1.3.0RC3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>