# CSRF Protection Implementation Guide

This document explains the CSRF (Cross-Site Request Forgery) protection system that has been implemented site-wide.

## Overview

The CSRF protection system provides comprehensive security against CSRF attacks while maintaining backward compatibility with existing code. It includes:

- Enhanced CSRF token generation and validation
- Backward compatibility with existing sessiontoken system
- JavaScript/AJAX support
- Easy-to-use helper functions
- Configurable protection levels

## Configuration

CSRF protection can be enabled/disabled in `includes/configure.php`:

```php
define('CSRF_PROTECTION', 'True'); // Set to 'False' to disable
```

## For Developers

### Adding CSRF Protection to Forms

#### Method 1: Using Enhanced tep_draw_form()
```php
// Automatically includes CSRF token when $tokenize = true
echo tep_draw_form('myform', 'process.php', 'post', '', true, 'my_action');
```

#### Method 2: Using tep_draw_secure_form()
```php
// Convenience function that automatically enables tokenization
echo tep_draw_secure_form('myform', 'process.php', 'post', '', 'my_action');
```

#### Method 3: Manual Token Addition
```php
echo '<form method="post" action="process.php">';
echo tep_draw_csrf_token_field('my_action');
echo '</form>';
```

#### Method 4: Using CSS Class (JavaScript will add token)
```html
<form method="post" action="process.php" class="csrf-protected">
    <!-- JavaScript will automatically add CSRF token -->
</form>
```

### Validating CSRF Tokens in Form Processors

#### Method 1: Using Middleware (Recommended)
```php
// At the top of your form processing script
require_once('includes/functions/csrf_middleware.php');
tep_csrf_require_valid_token('my_action');

// Your form processing code here...
```

#### Method 2: Manual Validation
```php
require_once('includes/functions/csrf_middleware.php');

if (!tep_csrf_check_token('my_action')) {
    // Handle invalid token
    tep_redirect('error.php');
}
```

#### Method 3: Backward Compatible Validation
```php
// This checks both new CSRF tokens and legacy sessiontoken
if (!tep_validate_form_token($_POST)) {
    // Handle invalid token
    tep_redirect('error.php');
}
```

### AJAX/JavaScript Support

#### Automatic CSRF Token Inclusion
The JavaScript CSRFManager automatically adds CSRF tokens to AJAX requests:

```javascript
// This will automatically include CSRF token
$.ajax({
    url: 'api.php',
    method: 'POST',
    data: { action: 'sendEmail', ... },
    success: function(response) { ... }
});
```

#### Manual Token Management
```javascript
// Get CSRF token
var token = CSRFManager.getToken('my_action');

// Add token to form data
var formData = { name: 'John', email: '<EMAIL>' };
formData = CSRFManager.addToFormData(formData, 'my_action');

// Add token to existing form
CSRFManager.addToForm('#myform', 'my_action');
```

#### API Endpoint for Token Retrieval
```javascript
// Get fresh CSRF token via API
$.get('api.php?getCSRFToken=my_action', function(response) {
    console.log('Token:', response.token);
});
```

### API Endpoint Protection

For API endpoints, use the API validation function:

```php
require_once('includes/functions/csrf_middleware.php');

$input_params = array_merge($_GET, $_POST);

if (!tep_csrf_validate_api($input_params, 'api_action')) {
    header('Content-Type: application/json');
    http_response_code(403);
    echo json_encode(['error' => 'Security validation failed']);
    exit;
}
```

## Security Features

### Token Properties
- Cryptographically secure random generation
- Action-specific tokens (optional)
- Time-based expiration (default: 1 hour)
- One-time use (tokens are consumed on validation)
- Session-bound (tied to user session)

### Automatic Cleanup
- Expired tokens are automatically cleaned up
- Cleanup runs randomly (5% chance per request)
- Prevents session bloat

### Backward Compatibility
- Existing sessiontoken validation continues to work
- Gradual migration path available
- No breaking changes to existing forms

## Testing

A test page is available at `test_csrf.php` to verify the implementation:

- Test CSRF token validation
- Test legacy sessiontoken validation
- Test AJAX requests with/without tokens
- View generated tokens and HTML elements

## Common Use Cases

### Contact Forms
```php
// In the form template
echo tep_draw_secure_form('contact', 'contact_process.php', 'post', '', 'contact_form');

// In contact_process.php
require_once('includes/functions/csrf_middleware.php');
tep_csrf_require_valid_token('contact_form');
```

### Shopping Cart Operations
```php
// For add to cart AJAX
CSRFManager.addToFormData(cartData, 'add_to_cart');

// In the API handler
if (!tep_csrf_validate_api($input_params, 'add_to_cart')) {
    // Return error
}
```

### Admin Forms
```php
// Admin forms should use specific action names
echo tep_draw_secure_form('admin_form', 'admin_process.php', 'post', '', 'admin_update_product');
```

## Migration Guide

### For Existing Forms
1. Add `class="csrf-protected"` to forms for automatic token inclusion
2. Or update `tep_draw_form()` calls to include `$tokenize = true`
3. Update form processors to use `tep_validate_form_token($_POST)`

### For New Forms
1. Use `tep_draw_secure_form()` for new forms
2. Use `tep_csrf_require_valid_token()` in processors
3. Use action-specific token names for better security

## Troubleshooting

### Common Issues
1. **Tokens not being included**: Check if forms have `csrf-protected` class or `$tokenize = true`
2. **AJAX requests failing**: Ensure CSRFManager.setupAjaxCSRF() is called
3. **Validation always failing**: Check if CSRF_PROTECTION is enabled in configure.php

### Debug Information
- Use `test_csrf.php` to view token information
- Check browser console for JavaScript errors
- Verify meta tag is present in page head

## Security Considerations

1. **Always validate tokens server-side** - Never rely only on JavaScript
2. **Use action-specific tokens** for sensitive operations
3. **Keep token lifetime reasonable** (default 1 hour is usually sufficient)
4. **Monitor for CSRF attempts** in server logs
5. **Use HTTPS** to prevent token interception

## Files Modified/Added

### New Files
- `includes/functions/security.php` - Core CSRF functions
- `includes/functions/csrf_middleware.php` - Validation middleware
- `test_csrf.php` - Test page
- `CSRF_PROTECTION_GUIDE.md` - This documentation

### Modified Files
- `includes/application_top.php` - Added security functions include
- `includes/functions/html_output.php` - Enhanced form functions
- `includes/template_top.php` - Added CSRF meta tag
- `includes/misc.js` - Added JavaScript CSRF manager
- `includes/configure.php` - Added CSRF configuration
- `enquiry_form.php` - Added CSRF protection
- `api.php` - Added API CSRF validation
- `address_book_process.php` - Enhanced token validation
