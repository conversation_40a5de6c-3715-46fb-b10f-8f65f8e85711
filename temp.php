<!--
******************************************************************************************************************************************************** <pre>
FIRST SQL: attributes.class.php > init_attributes() 144
string(340) "
			select distinct
				pa.attribute_default,
				popt.products_options_id,
				popt.products_options_name
			from
				products_attributes pa
				JOIN products_options popt on pa.options_id = popt.products_options_id
			where
				pa.products_id='12637' and
				popt.language_id = '1'
			order by
				pa.products_attributes_sort_order"

    ----------------------------------------------------------------------------
      Function: init_attributes, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/classes/attributes.class.php, Line: 232
        Arguments:
      Function: get_attributes, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 115
        Arguments:
      Function: getNewData, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 24
        Arguments:
         0: "12637"
         1: {"11":"261","1":"259","4":"256"}
         2: "{\\\"11\\\":262}"

----------------------------------------------------------------------------
******************************************************************************************************************************************************** </pre> --><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!--
******************************************************************************************************************************************************** <pre>
FIRST SQL: attributes.class.php > init_attributes() 144
string(340) "
			select distinct
				pa.attribute_default,
				popt.products_options_id,
				popt.products_options_name
			from
				products_attributes pa
				JOIN products_options popt on pa.options_id = popt.products_options_id
			where
				pa.products_id='12637' and
				popt.language_id = '1'
			order by
				pa.products_attributes_sort_order"

    ----------------------------------------------------------------------------
      Function: init_attributes, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/classes/attributes.class.php, Line: 249
        Arguments:
      Function: init_variations, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/classes/attributes.class.php, Line: 445
        Arguments:
      Function: get_variation_from_array, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/classes/attributes.class.php, Line: 358
        Arguments:
         0: {"11":262,"1":259,"4":256}
      Function: get_current_selected_variation, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/modules/content/product_info/cm_pi_name.php, Line: 41
        Arguments:
      Function: build_name_ui, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 170
        Arguments:
         0: {"products_id":12637,"products_name":"Xativa X-Press Matt Coated Paper Roll - 230gsm","products_desc...
         1: {"products_id":12637,"languages_id":1,"dependency_issues":[],"attributes":[],"options_values":[],"va...
      Function: getNewData, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 24
        Arguments:
         0: "12637"
         1: {"11":"261","1":"259","4":"256"}
         2: "{\\\"11\\\":262}"

----------------------------------------------------------------------------
******************************************************************************************************************************************************** </pre> --><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!--
******************************************************************************************************************************************************** <pre>
FIRST SQL: attributes.class.php > init_attributes() 144
string(340) "
			select distinct
				pa.attribute_default,
				popt.products_options_id,
				popt.products_options_name
			from
				products_attributes pa
				JOIN products_options popt on pa.options_id = popt.products_options_id
			where
				pa.products_id='12637' and
				popt.language_id = '1'
			order by
				pa.products_attributes_sort_order"

    ----------------------------------------------------------------------------
      Function: init_attributes, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/classes/attributes.class.php, Line: 232
        Arguments:
      Function: get_attributes, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/functions/tcs_functions.php, Line: 143
        Arguments:
         0: false
      Function: tcs_display_price, File: /var/www/vhosts/cadservices.co.uk/httpdocs/includes/modules/content/product_info/cm_pi_price.php, Line: 48
        Arguments:
         0: 12637
         1: "15.7600"
         2: 1
         3: true
         4: {"11":{"262":{"enabled":0,"products_attributes_id":3292,"products_options_name":"Type","products_opt...
      Function: build_price_ui, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 174
        Arguments:
         0: {"products_id":12637,"products_name":"Xativa X-Press Matt Coated Paper Roll - 230gsm","products_desc...
         1: {"11":{"262":{"enabled":0,"products_attributes_id":3292,"products_options_name":"Type","products_opt...
      Function: getNewData, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 24
        Arguments:
         0: "12637"
         1: {"11":"261","1":"259","4":"256"}
         2: "{\\\"11\\\":262}"

----------------------------------------------------------------------------
******************************************************************************************************************************************************** </pre> --><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '11'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '1'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- SECOND SQL: attributes.class.php > init_attributes() 176:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!-- thiurd SQL: attributes.class.php > init_attributes() 179:
select
pov.products_options_values_id,
pov.products_options_values_name,
pa.options_values_price,
pa.price_prefix,
pa.attribute_default,
pa.products_attributes_id,
pa.dependson_options_id,
pa.dependson_options_values_id,
pa.products_attributes_sort_order
from
products_attributes pa,
products_options_values pov
where
pa.products_id = '12637'
and pa.options_id = '4'
and pa.options_values_id = pov.products_options_values_id and pov.language_id = '1'
order by pa.products_attributes_sort_order
--><!--
******************************************************************************************************************************************************** <pre>
getNewData: api.php > getNewData() 179
array(10) {
  ["productId"]=>
  string(5) "12637"
  ["current_attributes"]=>
  array(3) {
    [11]=>
    string(3) "261"
    [1]=>
    string(3) "259"
    [4]=>
    string(3) "256"
  }
  ["selected_attribute"]=>
  string(12) "{\"11\":262}"
  ["$product_info_query_sql"]=>
  string(1110) "
    SELECT
            p.products_id,
            pd.products_name,
            pd.products_description,
            IF(p2c.products_to_categories_attribs_id, p2ca.products_model, p.products_model) AS products_model,
            p.products_quantity,
            p.products_image,
            pd.products_url,
            if (pa.products_id, pa.options_values_price + p.products_price, p.products_price) as products_price,
            p.products_tax_class_id,
            p.products_date_added,
            p.products_date_available,
            p.manufacturers_id,
            p.products_gtin
    FROM
        products p left join products_attributes pa on p.products_id = pa.products_id and pa.attribute_default = 1
        JOIN products_description pd ON pd.products_id = p.products_id
        JOIN products_to_categories p2c ON p2c.products_id = p.products_id
        LEFT JOIN products_to_categories_attrib p2ca on p2c.products_to_categories_attribs_id = p2ca.products_to_categories_attribs_id

    WHERE
        p.products_status = '1'
        AND p.products_id = '12637'
        AND pd.language_id = '1'"
  ["$product_info_query"]=>
  object(PDOStatement)#21 (1) {
    ["queryString"]=>
    string(1110) "
    SELECT
            p.products_id,
            pd.products_name,
            pd.products_description,
            IF(p2c.products_to_categories_attribs_id, p2ca.products_model, p.products_model) AS products_model,
            p.products_quantity,
            p.products_image,
            pd.products_url,
            if (pa.products_id, pa.options_values_price + p.products_price, p.products_price) as products_price,
            p.products_tax_class_id,
            p.products_date_added,
            p.products_date_available,
            p.manufacturers_id,
            p.products_gtin
    FROM
        products p left join products_attributes pa on p.products_id = pa.products_id and pa.attribute_default = 1
        JOIN products_description pd ON pd.products_id = p.products_id
        JOIN products_to_categories p2c ON p2c.products_id = p.products_id
        LEFT JOIN products_to_categories_attrib p2ca on p2c.products_to_categories_attribs_id = p2ca.products_to_categories_attribs_id

    WHERE
        p.products_status = '1'
        AND p.products_id = '12637'
        AND pd.language_id = '1'"
  }
  ["$product_info"]=>
  array(13) {
    ["products_id"]=>
    int(12637)
    ["products_name"]=>
    string(46) "Xativa X-Press Matt Coated Paper Roll - 230gsm"
    ["products_description"]=>
    string(12018) "<h2><span style="color:#2980b9;">XATIVA X-Press Matt&nbsp;Coated Paper - 230gsm</span></h2>

<p><span style="font-size: 14px;">For the highest standard in print quality, choose XATIVA X-Press Matt Coated Paper 230gsm</span><span style="font-size: 14px;">. With its ultra-heavyweight construction and superior matte coating, this paper is perfect for demanding print jobs that require rich color depth, precise line work, and a luxurious, professional finish.</span></p>

<p>&nbsp;</p>

<p><strong><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Key Features:</span></span></span></strong></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Weight: 230gsm &ndash; ultra-heavyweight for maximum durability and premium presentation</span></span></span></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Finish: High-quality matt coating for vibrant color, fine detail, and a smooth, non-reflective surface</span></span></span></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Size: 17 to 60&nbsp;inches&nbsp; &ndash; ideal for detailed graphics and full-color prints</span></span></span></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Ink Compatibility: Compatible with both dye and pigment-based inkjet printers</span></span></span></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Applications: Ideal for art prints, photo reproductions, exhibition graphics, architectural plans, and high-end marketing materials</span></span></span></p>

<p><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">When presentation is everything, XATIVA XXPMC230 delivers exceptional results that stand out.</span></span></span></p>

<p>&nbsp;</p>

<p><strong><span style="font-size:14px;"><span style="background:white"><span style="line-height:107%">Sizes Available:</span></span></span></strong></p>

<ul>
	<li>Xativa X-Press Matt Coated Paper Roll - 17in x 45m - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 17in x 45m 3in core - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 24in x 45m - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 24in x 45m 3in core - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 36in x 45m - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 36in x 45m 3in core - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 42in x 45m - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 42in x 45m 3in core - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 44in x 45m 3in core - 230gsm</li>
	<li>Xativa X-Press Matt Coated Paper Roll - 60in x 45m - 230gsm<br />
	&nbsp;</li>
</ul>

<p>&nbsp;</p>

<p>&nbsp;</p>

<h3><strong>Related Products:</strong></h3>

<table border="1" cellpadding="1" cellspacing="1" class="table-bordered" id="relatedProductsModuleContainer-0">
	<caption>
	<p style="text-align: left;">&nbsp;</p>
	</caption>
	<tbody>
		<tr>
			<td rowspan="1" style="text-align: center; border-color: rgb(0, 0, 0); background-color: rgb(219, 229, 241); vertical-align: middle;"><span style="font-family:Arial;"><span style="font-size:16px;"><b><span style="line-height: 107%; text-transform: uppercase;">DESCRIPTION</span></b></span></span></td>
			<td colspan="1" rowspan="1" style="text-align: center; border-color: rgb(0, 0, 0); background-color: rgb(219, 229, 241);"><span style="font-family:Arial;"><span style="font-size:16px;"><strong>&nbsp;&nbsp; &nbsp; Part No.&nbsp; &nbsp;&nbsp; </strong></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;"><span style="line-height: normal;">&nbsp;A2 Sheets &ndash; 420mm x 594mm x 250 sheets&nbsp;</span>Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-media/xativa-90gsm-colour-print-inkjet-plotter-paper-sheet-420mm-594mm-250-sheets-90gsm-p-1052.html">XCP90-A2 </a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;"><span style="line-height: normal;">&nbsp;A1 Sheets - 594mm x 841mm x 250 sheets&nbsp;</span>Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-media/xativa-90gsm-colour-print-inkjet-plotter-paper-sheet-594mm-841mm-250-sheets-90gsm-p-1051.html">XCP90-A1</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - A2 - 16&quot; - 420mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film-rolls/xativa-cad-printer-plotter-paper-rolls-box-90gsm-165-420mm-50m-p-9894.html"><span style="font-family:Arial;"><span style="font-size:14px;">XCP90-16-4R</span></span></a></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - A1 - 23&quot; - 594mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-box-rolls-colour-inkjet-paper-90gsm-594mm-50m-p-9825.html">XCP90-23-4R</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 1 roll - A1 - 23&quot; - 594mm x 110meter Colour Inkjet Paper 90gsm</span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film-rolls/xativa-110-meter-print-plotter-paper-roll-90gsm-841mm-110m-p-10620.html"><span style="font-family:Arial;">XCP90-23-110</span></a></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - A1+ - 24&quot; - 610mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-box-rolls-colour-inkjet-paper-90gsm-610mm-50m-p-151.html">XCP90-24-4R</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 1 roll - A1+ - 24&quot; - 610mm x 90meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-meter-roll-colour-inkjet-paper-90gsm-24in-610mm-90m-p-9264.html">XCP90-24L</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;">&nbsp;<span style="font-size:14px;">Box of 1 roll - A1+ - 24&quot; - 610mm x 110meter Colour Inkjet Paper 90gsm</span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film-rolls/xativa-110-meter-print-plotter-paper-roll-90gsm-24in-610mm-110m-p-10617.html">XCP90-24-110</a></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - A0 - 33&quot; - 841mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-box-rolls-colour-inkjet-paper-90gsm-841mm-50m-p-144.html">XCP90-33-4R</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 1 roll - A0 - 33&quot; - 841mm x 90meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-meter-roll-colour-print-inkjet-paper-90gsm-841mm-90m-p-6818.html">XCP90-33L</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;">&nbsp;<span style="font-size:14px;">Box of 1 roll - A0 - 33&quot; - 841mm x 110meter Colour Inkjet Paper 90gsm</span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film-rolls/xativa-110-meter-print-plotter-paper-roll-90gsm-841mm-110m-p-10618.html"><span style="font-family:Arial;">XCP90-33-110</span></a></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - A0+ - 36&quot; - 914mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-box-rolls-colour-inkjet-paper-90gsm-914mm-50m-p-145.html">XCP90-36-4R</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 1 roll - A0+ - 36&quot; - 914mm x 90meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-meter-roll-colour-print-inkjet-paper-90gsm-914mm-90m-p-147.html">XCP90-36L</a></span></span></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;">&nbsp;<span style="font-size:14px;">Box of 1 roll - A0+ 36&quot; - 914mm x 110meter Colour Inkjet Paper 90gsm</span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film-rolls/xativa-110-meter-print-plotter-paper-roll-90gsm-841mm-110m-p-10619.html"><span style="font-family:Arial;">XCP90-36-110</span></a></td>
		</tr>
		<tr>
			<td style="border-color: rgb(0, 0, 0); text-align: left;"><span style="font-family:Arial;"><span style="font-size:14px;">&nbsp;Box of 4 rolls - 42&quot; - 1067mm x 50meter Colour Inkjet Paper 90gsm&nbsp; </span></span></td>
			<td style="border-color: rgb(0, 0, 0); text-align: center;"><span style="font-family:Arial;"><span style="font-size:14px;"><a href="https://www.cadservices.co.uk/papers-films-cad-paper-film/xativa-box-rolls-colour-inkjet-paper-90gsm-1067mm-50m-p-7390.html">XCP90-42-4R</a></span></span></td>
		</tr>
	</tbody>
</table>

<p>&nbsp;</p>"
    ["products_model"]=>
    string(8) "XXPMC230"
    ["products_quantity"]=>
    int(999)
    ["products_image"]=>
    string(20) "FSC.Solo-360x320.jpg"
    ["products_url"]=>
    string(0) ""
    ["products_price"]=>
    string(7) "15.7600"
    ["products_tax_class_id"]=>
    int(1)
    ["products_date_added"]=>
    string(19) "2025-04-23 12:38:07"
    ["products_date_available"]=>
    string(19) "0000-00-00 00:00:00"
    ["manufacturers_id"]=>
    int(13)
    ["products_gtin"]=>
    string(4) "null"
  }
  ["$products_attributes"]=>
  object(tcs_product_attributes)#20 (19) {
    ["products_id"]=>
    int(12637)
    ["languages_id"]=>
    int(1)
    ["dependency_issues"]=>
    array(0) {
    }
    ["attributes"]=>
    array(3) {
      [11]=>
      array(3) {
        ["products_options_name"]=>
        string(4) "Type"
        ["attribute_default"]=>
        bool(false)
        ["values"]=>
        array(2) {
          [261]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3291)
            ["products_options_name"]=>
            string(4) "Type"
            ["products_options_id"]=>
            int(11)
            ["products_options_values_name"]=>
            string(8) "2in Core"
            ["products_options_values_id"]=>
            int(261)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(1)
            ["dependson_options_id"]=>
            NULL
            ["dependson_options_values_id"]=>
            NULL
            ["products_attributes_sort_order"]=>
            int(0)
          }
          [262]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3292)
            ["products_options_name"]=>
            string(4) "Type"
            ["products_options_id"]=>
            int(11)
            ["products_options_values_name"]=>
            string(8) "3in Core"
            ["products_options_values_id"]=>
            int(262)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            NULL
            ["dependson_options_values_id"]=>
            NULL
            ["products_attributes_sort_order"]=>
            int(1)
          }
        }
      }
      [1]=>
      array(3) {
        ["products_options_name"]=>
        string(4) "Size"
        ["attribute_default"]=>
        bool(false)
        ["values"]=>
        array(6) {
          [259]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3286)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "17in"
            ["products_options_values_id"]=>
            int(259)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(1)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(7) "261,262"
            ["products_attributes_sort_order"]=>
            int(2)
          }
          [197]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3287)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "24in"
            ["products_options_values_id"]=>
            int(197)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(7) "261,262"
            ["products_attributes_sort_order"]=>
            int(3)
          }
          [198]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3290)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "36in"
            ["products_options_values_id"]=>
            int(198)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(7) "261,262"
            ["products_attributes_sort_order"]=>
            int(4)
          }
          [246]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3289)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "42in"
            ["products_options_values_id"]=>
            int(246)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(7) "261,262"
            ["products_attributes_sort_order"]=>
            int(5)
          }
          [199]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3294)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "44in"
            ["products_options_values_id"]=>
            int(199)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(3) "262"
            ["products_attributes_sort_order"]=>
            int(6)
          }
          [254]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3295)
            ["products_options_name"]=>
            string(4) "Size"
            ["products_options_id"]=>
            int(1)
            ["products_options_values_name"]=>
            string(4) "60in"
            ["products_options_values_id"]=>
            int(254)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(0)
            ["dependson_options_id"]=>
            int(11)
            ["dependson_options_values_id"]=>
            string(3) "261"
            ["products_attributes_sort_order"]=>
            int(7)
          }
        }
      }
      [4]=>
      array(3) {
        ["products_options_name"]=>
        string(6) "Length"
        ["attribute_default"]=>
        bool(true)
        ["values"]=>
        array(1) {
          [256]=>
          array(12) {
            ["enabled"]=>
            int(1)
            ["products_attributes_id"]=>
            int(3288)
            ["products_options_name"]=>
            string(6) "Length"
            ["products_options_id"]=>
            int(4)
            ["products_options_values_name"]=>
            string(3) "45m"
            ["products_options_values_id"]=>
            int(256)
            ["options_values_price"]=>
            string(6) "0.0000"
            ["price_prefix"]=>
            string(1) "+"
            ["attribute_default"]=>
            int(1)
            ["dependson_options_id"]=>
            NULL
            ["dependson_options_values_id"]=>
            NULL
            ["products_attributes_sort_order"]=>
            int(8)
          }
        }
      }
    }
    ["options_values"]=>
    array(9) {
      [261]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3291)
        ["products_options_name"]=>
        string(4) "Type"
        ["products_options_id"]=>
        int(11)
        ["products_options_values_name"]=>
        string(8) "2in Core"
        ["products_options_values_id"]=>
        int(261)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        NULL
        ["dependson_options_values_id"]=>
        NULL
        ["products_attributes_sort_order"]=>
        int(0)
      }
      [262]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3292)
        ["products_options_name"]=>
        string(4) "Type"
        ["products_options_id"]=>
        int(11)
        ["products_options_values_name"]=>
        string(8) "3in Core"
        ["products_options_values_id"]=>
        int(262)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        NULL
        ["dependson_options_values_id"]=>
        NULL
        ["products_attributes_sort_order"]=>
        int(1)
      }
      [259]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3286)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "17in"
        ["products_options_values_id"]=>
        int(259)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(7) "261,262"
        ["products_attributes_sort_order"]=>
        int(2)
      }
      [197]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3287)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "24in"
        ["products_options_values_id"]=>
        int(197)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(7) "261,262"
        ["products_attributes_sort_order"]=>
        int(3)
      }
      [198]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3290)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "36in"
        ["products_options_values_id"]=>
        int(198)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(7) "261,262"
        ["products_attributes_sort_order"]=>
        int(4)
      }
      [246]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3289)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "42in"
        ["products_options_values_id"]=>
        int(246)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(7) "261,262"
        ["products_attributes_sort_order"]=>
        int(5)
      }
      [199]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3294)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "44in"
        ["products_options_values_id"]=>
        int(199)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(3) "262"
        ["products_attributes_sort_order"]=>
        int(6)
      }
      [254]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3295)
        ["products_options_name"]=>
        string(4) "Size"
        ["products_options_id"]=>
        int(1)
        ["products_options_values_name"]=>
        string(4) "60in"
        ["products_options_values_id"]=>
        int(254)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(0)
        ["dependson_options_id"]=>
        int(11)
        ["dependson_options_values_id"]=>
        string(3) "261"
        ["products_attributes_sort_order"]=>
        int(7)
      }
      [256]=>
      array(12) {
        ["enabled"]=>
        int(0)
        ["products_attributes_id"]=>
        int(3288)
        ["products_options_name"]=>
        string(6) "Length"
        ["products_options_id"]=>
        int(4)
        ["products_options_values_name"]=>
        string(3) "45m"
        ["products_options_values_id"]=>
        int(256)
        ["options_values_price"]=>
        string(6) "0.0000"
        ["price_prefix"]=>
        string(1) "+"
        ["attribute_default"]=>
        int(1)
        ["dependson_options_id"]=>
        NULL
        ["dependson_options_values_id"]=>
        NULL
        ["products_attributes_sort_order"]=>
        int(8)
      }
    }
    ["variations"]=>
    array(10) {
      [0]=>
      array(15) {
        ["products_variations_id"]=>
        int(2973)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}259{11}261"
        ["product_name_suffix"]=>
        string(21) ", 45m, 17in, 2in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(11) "XXPMC230-17"
        ["price"]=>
        string(7) "20.7300"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(259)
          [11]=>
          int(261)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [1]=>
      array(15) {
        ["products_variations_id"]=>
        int(2975)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}197{11}261"
        ["product_name_suffix"]=>
        string(21) ", 45m, 24in, 2in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(11) "XXPMC230-24"
        ["price"]=>
        string(7) "23.1400"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(197)
          [11]=>
          int(261)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [2]=>
      array(15) {
        ["products_variations_id"]=>
        int(2977)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}198{11}261"
        ["product_name_suffix"]=>
        string(21) ", 45m, 36in, 2in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(11) "XXPMC230-36"
        ["price"]=>
        string(7) "33.2500"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(198)
          [11]=>
          int(261)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [3]=>
      array(15) {
        ["products_variations_id"]=>
        int(2982)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}254{11}261"
        ["product_name_suffix"]=>
        string(21) ", 45m, 60in, 2in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(11) "XXPMC230-60"
        ["price"]=>
        string(7) "44.0800"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(254)
          [11]=>
          int(261)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [4]=>
      array(15) {
        ["products_variations_id"]=>
        int(2979)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}246{11}261"
        ["product_name_suffix"]=>
        string(21) ", 45m, 42in, 2in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(11) "XXPMC230-42"
        ["price"]=>
        string(7) "38.7100"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(246)
          [11]=>
          int(261)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [5]=>
      array(15) {
        ["products_variations_id"]=>
        int(2974)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}259{11}262"
        ["product_name_suffix"]=>
        string(21) ", 45m, 17in, 3in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(13) "XXPMC230-17-3"
        ["price"]=>
        string(7) "20.7300"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(259)
          [11]=>
          int(262)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [6]=>
      array(15) {
        ["products_variations_id"]=>
        int(2976)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}197{11}262"
        ["product_name_suffix"]=>
        string(21) ", 45m, 24in, 3in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(13) "XXPMC230-24-3"
        ["price"]=>
        string(7) "23.1400"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(197)
          [11]=>
          int(262)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [7]=>
      array(15) {
        ["products_variations_id"]=>
        int(2978)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}198{11}262"
        ["product_name_suffix"]=>
        string(21) ", 45m, 36in, 3in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(13) "XXPMC230-36-3"
        ["price"]=>
        string(7) "33.2500"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(198)
          [11]=>
          int(262)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [8]=>
      array(15) {
        ["products_variations_id"]=>
        int(2980)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}246{11}262"
        ["product_name_suffix"]=>
        string(21) ", 45m, 42in, 3in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(13) "XXPMC230-42-3"
        ["price"]=>
        string(7) "38.7100"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(246)
          [11]=>
          int(262)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
      [9]=>
      array(15) {
        ["products_variations_id"]=>
        int(2981)
        ["autodesk_catalog_unique_hash"]=>
        string(4) "NULL"
        ["products_id"]=>
        int(12637)
        ["attribute_string"]=>
        string(19) "{4}256{1}199{11}262"
        ["product_name_suffix"]=>
        string(21) ", 45m, 44in, 3in Core"
        ["manufacturers_id"]=>
        int(13)
        ["model"]=>
        string(13) "XXPMC230-44-3"
        ["price"]=>
        string(7) "40.6400"
        ["gtin"]=>
        string(4) "NULL"
        ["image_url"]=>
        string(0) ""
        ["image_id"]=>
        NULL
        ["attributes"]=>
        array(3) {
          [4]=>
          int(256)
          [1]=>
          int(199)
          [11]=>
          int(262)
        }
        ["autodesk_link"]=>
        string(0) ""
        ["autodesk_link_name"]=>
        string(0) ""
        ["enabled"]=>
        int(1)
      }
    }
    ["has_attributes"]=>
    bool(true)
    ["has_variations"]=>
    bool(true)
    ["default_attributes"]=>
    array(3) {
      [11]=>
      int(261)
      [1]=>
      int(259)
      [4]=>
      int(256)
    }
    ["selected_attributes"]=>
    array(3) {
      [11]=>
      array(1) {
        [262]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3292)
          ["products_options_name"]=>
          string(4) "Type"
          ["products_options_id"]=>
          int(11)
          ["products_options_values_name"]=>
          string(8) "3in Core"
          ["products_options_values_id"]=>
          int(262)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(0)
          ["dependson_options_id"]=>
          NULL
          ["dependson_options_values_id"]=>
          NULL
          ["products_attributes_sort_order"]=>
          int(1)
        }
      }
      [1]=>
      array(1) {
        [259]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3286)
          ["products_options_name"]=>
          string(4) "Size"
          ["products_options_id"]=>
          int(1)
          ["products_options_values_name"]=>
          string(4) "17in"
          ["products_options_values_id"]=>
          int(259)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(1)
          ["dependson_options_id"]=>
          int(11)
          ["dependson_options_values_id"]=>
          string(7) "261,262"
          ["products_attributes_sort_order"]=>
          int(2)
        }
      }
      [4]=>
      array(1) {
        [256]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3288)
          ["products_options_name"]=>
          string(6) "Length"
          ["products_options_id"]=>
          int(4)
          ["products_options_values_name"]=>
          string(3) "45m"
          ["products_options_values_id"]=>
          int(256)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(1)
          ["dependson_options_id"]=>
          NULL
          ["dependson_options_values_id"]=>
          NULL
          ["products_attributes_sort_order"]=>
          int(8)
        }
      }
    }
    ["selected_variation"]=>
    array(0) {
    }
    ["input_attributes"]=>
    array(3) {
      [11]=>
      int(262)
      [1]=>
      int(259)
      [4]=>
      int(256)
    }
    ["isPreset"]=>
    bool(true)
    ["cacheKey":"tcs_product_attributes":private]=>
    string(24) "product_attributes_12637"
    ["cacheTime":"tcs_product_attributes":private]=>
    int(3600)
    ["dirty":"tcs_product_attributes":private]=>
    bool(false)
    ["building":"tcs_product_attributes":private]=>
    bool(false)
    ["interlinked"]=>
    bool(false)
    ["error_checker"]=>
    object(tcs_product_attributes_error_checker)#22 (1) {
      ["attributes_instance":"tcs_product_attributes_error_checker":private]=>
      *RECURSION*
    }
  }
  ["$attributes_selector"]=>
  array(3) {
    ["html"]=>
    string(4439) "<div class="col-sm-12 cm-pi-options-attributes" id="cm-pi-options-attributes">
  <h4 class="h3">Available Options <span class="small">(Click the buttons to select desired options.)</span></h4>

  <div class="form-group prod_attribute_group ">
                                            <label for="input_11" class="control-label col-sm-3">Type</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[11]" value="262"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="11" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":261}}' id="product_attribute_btns_11_261" class="btn btn-default product_attribute_btns ">2in Core</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":262}}' id="product_attribute_btns_11_262" class="btn btn-default product_attribute_btns active btn-success">3in Core</button>
                		</div>
                	</div>
				</div>
				<div class="form-group prod_attribute_group ">
                                            <label for="input_1" class="control-label col-sm-3">Size</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[1]" value="259"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="1" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":259}}' id="product_attribute_btns_1_259" class="btn btn-default product_attribute_btns active btn-success">17in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":197}}' id="product_attribute_btns_1_197" class="btn btn-default product_attribute_btns ">24in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":198}}' id="product_attribute_btns_1_198" class="btn btn-default product_attribute_btns ">36in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":246}}' id="product_attribute_btns_1_246" class="btn btn-default product_attribute_btns ">42in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":199}}' id="product_attribute_btns_1_199" class="btn btn-default product_attribute_btns ">44in</button>
                		</div>
                	</div>
				</div>
				<div class="form-group prod_attribute_group ">
                                            <label for="input_4" class="control-label col-sm-3">Length</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[4]" value="256"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="4" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes"  hx-swap="outerHTML"  hx-vals='{"selected_attribute":{"4":256}}' id="product_attribute_btns_4_256" class="btn_debuttoned">45m</button>
                		</div>
                	</div>
				</div>

</div>
"
    ["selected_attributes"]=>
    array(3) {
      [11]=>
      array(1) {
        [262]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3292)
          ["products_options_name"]=>
          string(4) "Type"
          ["products_options_id"]=>
          int(11)
          ["products_options_values_name"]=>
          string(8) "3in Core"
          ["products_options_values_id"]=>
          int(262)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(0)
          ["dependson_options_id"]=>
          NULL
          ["dependson_options_values_id"]=>
          NULL
          ["products_attributes_sort_order"]=>
          int(1)
        }
      }
      [1]=>
      array(1) {
        [259]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3286)
          ["products_options_name"]=>
          string(4) "Size"
          ["products_options_id"]=>
          int(1)
          ["products_options_values_name"]=>
          string(4) "17in"
          ["products_options_values_id"]=>
          int(259)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(1)
          ["dependson_options_id"]=>
          int(11)
          ["dependson_options_values_id"]=>
          string(7) "261,262"
          ["products_attributes_sort_order"]=>
          int(2)
        }
      }
      [4]=>
      array(1) {
        [256]=>
        array(12) {
          ["enabled"]=>
          int(0)
          ["products_attributes_id"]=>
          int(3288)
          ["products_options_name"]=>
          string(6) "Length"
          ["products_options_id"]=>
          int(4)
          ["products_options_values_name"]=>
          string(3) "45m"
          ["products_options_values_id"]=>
          int(256)
          ["options_values_price"]=>
          string(6) "0.0000"
          ["price_prefix"]=>
          string(1) "+"
          ["attribute_default"]=>
          int(1)
          ["dependson_options_id"]=>
          NULL
          ["dependson_options_values_id"]=>
          NULL
          ["products_attributes_sort_order"]=>
          int(8)
        }
      }
    }
    ["last_count"]=>
    int(1)
  }
  ["$response"]=>
  string(7086) " <div class="col-sm-12 cm-pi-name" style="min-height:70px" id="cm-pi-name" hx-swap-oob="true">
  <div class="page-header">
    <h1 class="h1"><a href="https://www.cadservices.co.uk/papers-films-xativa-media-matte-coated/xativa-press-matt-coated-paper-roll-230gsm-p-12637.html" itemprop="url"><span id="products_name" itemprop="name">Xativa X-Press Matt Coated Paper Roll - 230gsm</span></a></h1>
  </div>
</div>
<div class="col-sm-6 cm-pi-model" id="cm-pi-model" hx-swap-oob="true">
	<dl class="dl-horizontal list-group-item-text small">
		<dt>Manufacturer</dt><dd id="products_manufacturer">Xativa</dd>
		<dt>Product Code</dt><dd id="products_model">XXPMC230</dd>
				<dl class="dl-horizontal list-group-item-text small">
	</dl>
</div>
<div class="col-sm-6 cm-pi-price" id="cm-pi-price" hx-swap-oob="true">
  <div class="">
    <div class="productsPrice text-right-not-xs">
    <div class="text-right-not-xs productsPrice"><span id="productInfoPrice">£15.76</span><br> <span id="productsPriceIncTax">(ex VAT £18.91 Inc. VAT)</span></div>
	</div>
  </div>
</div>

<div class="col-xs-12 text-right cm-pi-buy-button" hx-swap-oob="true">
  <div class="page-header">
	<button  type="submit" hx-post="api.php" hx-target="#navbar_shopping_cart" hx-swap="outerHTML" hx-vals= '{"addToCart":1,"products_id":12637,"products_name":"Xativa X-Press Matt Coated Paper Roll - 230gsm","attribute_string":{"11":{"262":{"enabled":0,"products_attributes_id":3292,"products_options_name":"Type","products_options_id":11,"products_options_values_name":"3in Core","products_options_values_id":262,"options_values_price":"0.0000","price_prefix":"+","attribute_default":0,"dependson_options_id":null,"dependson_options_values_id":null,"products_attributes_sort_order":1}},"1":{"259":{"enabled":0,"products_attributes_id":3286,"products_options_name":"Size","products_options_id":1,"products_options_values_name":"17in","products_options_values_id":259,"options_values_price":"0.0000","price_prefix":"+","attribute_default":1,"dependson_options_id":11,"dependson_options_values_id":"261,262","products_attributes_sort_order":2}},"4":{"256":{"enabled":0,"products_attributes_id":3288,"products_options_name":"Length","products_options_id":4,"products_options_values_name":"45m","products_options_values_id":256,"options_values_price":"0.0000","price_prefix":"+","attribute_default":1,"dependson_options_id":null,"dependson_options_values_id":null,"products_attributes_sort_order":8}}}}' class="btn btn-success btn-product-info btn-buy"> <span class="fa fa-shopping-cart"></span> add to cart</button><input type="hidden" name="products_id" value="12637" />	</div>
</div>
<div class="col-sm-12 cm-pi-options-attributes" id="cm-pi-options-attributes">
  <h4 class="h3">Available Options <span class="small">(Click the buttons to select desired options.)</span></h4>

  <div class="form-group prod_attribute_group ">
                                            <label for="input_11" class="control-label col-sm-3">Type</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[11]" value="262"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="11" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":261}}' id="product_attribute_btns_11_261" class="btn btn-default product_attribute_btns ">2in Core</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":262}}' id="product_attribute_btns_11_262" class="btn btn-default product_attribute_btns active btn-success">3in Core</button>
                		</div>
                	</div>
				</div>
				<div class="form-group prod_attribute_group ">
                                            <label for="input_1" class="control-label col-sm-3">Size</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[1]" value="259"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="1" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":259}}' id="product_attribute_btns_1_259" class="btn btn-default product_attribute_btns active btn-success">17in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":197}}' id="product_attribute_btns_1_197" class="btn btn-default product_attribute_btns ">24in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":198}}' id="product_attribute_btns_1_198" class="btn btn-default product_attribute_btns ">36in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":246}}' id="product_attribute_btns_1_246" class="btn btn-default product_attribute_btns ">42in</button>
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":199}}' id="product_attribute_btns_1_199" class="btn btn-default product_attribute_btns ">44in</button>
                		</div>
                	</div>
				</div>
				<div class="form-group prod_attribute_group ">
                                            <label for="input_4" class="control-label col-sm-3">Length</label>
                                                <div class="col-sm-9 attributeWrapper">
                            <input type="hidden" name="attributes[4]" value="256"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="4" >
                        <button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes"  hx-swap="outerHTML"  hx-vals='{"selected_attribute":{"4":256}}' id="product_attribute_btns_4_256" class="btn_debuttoned">45m</button>
                		</div>
                	</div>
				</div>

</div>
"
  ["$time"]=>
  array(8) {
    [0]=>
    string(56) "API getNewData execution time: 0.013432025909424 seconds"
    [1]=>
    string(82) "API getNewData after products_attributes execution time: 0.020757913589478 seconds"
    [2]=>
    string(81) "API getNewData after product_info_query execution time: 0.021516799926758 seconds"
    [3]=>
    string(81) "API getNewData after options attributes execution time: 0.023638963699341 seconds"
    [4]=>
    string(67) "API getNewData after name execution time: 0.031095027923584 seconds"
    [5]=>
    string(68) "API getNewData after model execution time: 0.031741857528687 seconds"
    [6]=>
    string(68) "API getNewData after price execution time: 0.040047883987427 seconds"
    [7]=>
    string(73) "API getNewData after buy button execution time: 0.040287017822266 seconds"
  }
}

    ----------------------------------------------------------------------------
      Function: getNewData, File: /var/www/vhosts/cadservices.co.uk/httpdocs/api.php, Line: 24
        Arguments:
         0: "12637"
         1: {"11":"261","1":"259","4":"256"}
         2: "{\\\"11\\\":262}"

----------------------------------------------------------------------------
******************************************************************************************************************************************************** </pre> --><div class="col-sm-12 cm-pi-name" style="min-height:70px" id="cm-pi-name" hx-swap-oob="true"><div class="page-header"><h1 class="h1"><a href="https://www.cadservices.co.uk/papers-films-xativa-media-matte-coated/xativa-press-matt-coated-paper-roll-230gsm-p-12637.html" itemprop="url"><span id="products_name" itemprop="name">Xativa X-Press Matt Coated Paper Roll - 230gsm</span></a></h1></div></div><div class="col-sm-6 cm-pi-model" id="cm-pi-model" hx-swap-oob="true"><dl class="dl-horizontal list-group-item-text small"><dt>Manufacturer</dt><dd id="products_manufacturer">Xativa</dd><dt>Product Code</dt><dd id="products_model">XXPMC230</dd><dl class="dl-horizontal list-group-item-text small"></dl></div><div class="col-sm-6 cm-pi-price" id="cm-pi-price" hx-swap-oob="true"><div class=""><div class="productsPrice text-right-not-xs"><div class="text-right-not-xs productsPrice"><span id="productInfoPrice">£15.76</span><br><span id="productsPriceIncTax">(ex VAT £18.91 Inc. VAT)</span></div></div></div></div><div class="col-xs-12 text-right cm-pi-buy-button" hx-swap-oob="true"><div class="page-header"><button  type="submit" hx-post="api.php" hx-target="#navbar_shopping_cart" hx-swap="outerHTML" hx-vals= '{"addToCart":1,"products_id":12637,"products_name":"Xativa X-Press Matt Coated Paper Roll - 230gsm","attribute_string":{"11":{"262":{"enabled":0,"products_attributes_id":3292,"products_options_name":"Type","products_options_id":11,"products_options_values_name":"3in Core","products_options_values_id":262,"options_values_price":"0.0000","price_prefix":"+","attribute_default":0,"dependson_options_id":null,"dependson_options_values_id":null,"products_attributes_sort_order":1}},"1":{"259":{"enabled":0,"products_attributes_id":3286,"products_options_name":"Size","products_options_id":1,"products_options_values_name":"17in","products_options_values_id":259,"options_values_price":"0.0000","price_prefix":"+","attribute_default":1,"dependson_options_id":11,"dependson_options_values_id":"261,262","products_attributes_sort_order":2}},"4":{"256":{"enabled":0,"products_attributes_id":3288,"products_options_name":"Length","products_options_id":4,"products_options_values_name":"45m","products_options_values_id":256,"options_values_price":"0.0000","price_prefix":"+","attribute_default":1,"dependson_options_id":null,"dependson_options_values_id":null,"products_attributes_sort_order":8}}}}' class="btn btn-success btn-product-info btn-buy"><span class="fa fa-shopping-cart"></span> add to cart</button><input type="hidden" name="products_id" value="12637" /></div></div><div class="col-sm-12 cm-pi-options-attributes" id="cm-pi-options-attributes"><h4 class="h3">Available Options <span class="small">(Click the buttons to select desired options.)</span></h4><div class="form-group prod_attribute_group "><label for="input_11" class="control-label col-sm-3">Type</label><div class="col-sm-9 attributeWrapper"><input type="hidden" name="attributes[11]" value="262"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="11" ><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":261}}' id="product_attribute_btns_11_261" class="btn btn-default product_attribute_btns ">2in Core</button><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"11":262}}' id="product_attribute_btns_11_262" class="btn btn-default product_attribute_btns active btn-success">3in Core</button></div></div></div><div class="form-group prod_attribute_group "><label for="input_1" class="control-label col-sm-3">Size</label><div class="col-sm-9 attributeWrapper"><input type="hidden" name="attributes[1]" value="259"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="1" ><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":259}}' id="product_attribute_btns_1_259" class="btn btn-default product_attribute_btns active btn-success">17in</button><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":197}}' id="product_attribute_btns_1_197" class="btn btn-default product_attribute_btns ">24in</button><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":198}}' id="product_attribute_btns_1_198" class="btn btn-default product_attribute_btns ">36in</button><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":246}}' id="product_attribute_btns_1_246" class="btn btn-default product_attribute_btns ">42in</button><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes" hx-swap="outerHTML" hx-vals='{"selected_attribute":{"1":199}}' id="product_attribute_btns_1_199" class="btn btn-default product_attribute_btns ">44in</button></div></div></div><div class="form-group prod_attribute_group "><label for="input_4" class="control-label col-sm-3">Length</label><div class="col-sm-9 attributeWrapper"><input type="hidden" name="attributes[4]" value="256"><div id="attributes_values_id_btns_container" class="attributes_values_id_btns_container btn-group" data-optionid="4" ><button type="button" onclick='this.classList.add("active", "btn-success");'  hx-post="api.php?getNewData=12637" hx-target="#cm-pi-options-attributes"  hx-swap="outerHTML"  hx-vals='{"selected_attribute":{"4":256}}' id="product_attribute_btns_4_256" class="btn_debuttoned">45m</button></div></div></div></div>