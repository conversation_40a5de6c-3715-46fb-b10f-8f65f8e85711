<?php
// Debug and fix tax configuration
include('includes/application_top.php');

echo "<h2>Debug and Fix Tax Configuration</h2>";

echo "<h3>Current Configuration Values</h3>";
$query = tep_db_query("SELECT configuration_key, configuration_value FROM configuration WHERE configuration_key LIKE '%TCSSHIP%TAX%' ORDER BY configuration_key");
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Configuration Key</th><th>Current Value</th></tr>";
while ($row = tep_db_fetch_array($query)) {
    echo "<tr><td>" . $row['configuration_key'] . "</td><td>" . $row['configuration_value'] . "</td></tr>";
}
echo "</table>";

echo "<h3>Tax Classes Available</h3>";
$query = tep_db_query("SELECT tax_class_id, tax_class_title FROM tax_class ORDER BY tax_class_id");
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Tax Class ID</th><th>Tax Class Title</th></tr>";
while ($row = tep_db_fetch_array($query)) {
    echo "<tr><td>" . $row['tax_class_id'] . "</td><td>" . $row['tax_class_title'] . "</td></tr>";
}
echo "</table>";

echo "<h3>Tax Rates for Each Class</h3>";
$query = tep_db_query("SELECT tc.tax_class_id, tc.tax_class_title, tr.tax_rate FROM tax_class tc LEFT JOIN tax_rates tr ON tc.tax_class_id = tr.tax_class_id ORDER BY tc.tax_class_id");
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Tax Class ID</th><th>Tax Class Title</th><th>Tax Rate</th></tr>";
while ($row = tep_db_fetch_array($query)) {
    echo "<tr><td>" . $row['tax_class_id'] . "</td><td>" . $row['tax_class_title'] . "</td><td>" . ($row['tax_rate'] ?? 'No rate defined') . "%</td></tr>";
}
echo "</table>";

echo "<h3>Fixing Configuration</h3>";

// Update the configuration to use tax class 1
$update_query = "UPDATE configuration SET configuration_value = '1' WHERE configuration_key = 'MODULE_SHIPPING_TCSSHIP_TAX_CLASS'";
if (tep_db_query($update_query)) {
    echo "<p style='color: green;'>✓ Updated MODULE_SHIPPING_TCSSHIP_TAX_CLASS to 1</p>";
} else {
    echo "<p style='color: red;'>✗ Failed to update configuration</p>";
}

// Verify the change
echo "<h3>Updated Configuration</h3>";
$query = tep_db_query("SELECT configuration_key, configuration_value FROM configuration WHERE configuration_key LIKE '%TCSSHIP%TAX%' ORDER BY configuration_key");
echo "<table border='1' cellpadding='5'>";
echo "<tr><th>Configuration Key</th><th>Current Value</th></tr>";
while ($row = tep_db_fetch_array($query)) {
    echo "<tr><td>" . $row['configuration_key'] . "</td><td>" . $row['configuration_value'] . "</td></tr>";
}
echo "</table>";

echo "<h3>Testing Tax Rate Lookup</h3>";
// Test what tax rate we get for each class
for ($i = 1; $i <= 2; $i++) {
    $test_rate = tep_get_tax_rate($i, 222, 0); // UK country ID is typically 222
    $test_desc = tep_get_tax_description($i, 222, 0);
    echo "<p>Tax Class $i: Rate = $test_rate%, Description = '$test_desc'</p>";
}

echo "<p><strong>Configuration has been updated. Please test with a new order.</strong></p>";
echo "<p><em>You can delete this file after checking the results.</em></p>";
?>
