<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>401 Authorization Required</title>
  <link rel="stylesheet" href="/error_docs/styles.css">
</head>
<body>
<div class="page">
  <div class="main">
    <h1>Server Error</h1>
    <div class="error-code">401</div>
    <h2>Authorization Required</h2>
    <div class="error-description">
      <p class="lead">This server could not verify that you are authorized to access the document requested.  Either you supplied the wrong credentials (e.g., bad password), or your browser doesn't understand how to supply the credentials required.</p>
      <hr/>
    </div>
    <p>That's what you can do</p>
    <div class="help-actions">
      <a href="javascript:location.reload();">Reload Page</a>
      <a href="javascript:history.back();">Back to Previous Page</a>
      <a href="/">Home Page</a>
    </div>
  </div>
</div>
</body>
</html>
