<?php
// WebMakers.com Added: FREE-CALL FOR PRICE-COMING SOON ETC. v3.0
// Add to english.php - require('includes/languages/' . $language . '/free_call_for_price.php');

// PRICE BASED CONTROLS:

// FREE - PRICE IS SET TO $0
define('FREE_TEXT', 'FREE!');
define('FREE_IMAGE_ON','0');
define('FREE_IMAGE','free_prices.jpg');

// CALL FOR PRICE - PRICE IS SET TO $999999
define('CALL_TEXT', 'Call us for prices!');
define('CALL_LINK_ON','1');
define('CALL_LINK_TEXT','Click Here to Contact Us');
define('CALL_LINK_OFF_TEXT','Call our sales team on 0800 458 0947');
define('CALL_INCART_LINK', '<B><A HREF="' . DIR_WS_CATALOG . 'contact_us.php">' . CALL_LINK_TEXT . '</A></B> &nbsp;&nbsp; ');
define('CALL_IMAGE_ON','0');
define('CALL_IMAGE','callfor_prices.jpg');

// COMING SOON - PRICE IS SET TO $888888
define('SOON_TEXT', 'Coming Soon ...');
define('SOON_LINK_ON','0');
define('SOON_LINK_TEXT','Click Here to Contact Us');
define('SOON_LINK_OFF_TEXT','Call: 1-800-555-1212');
define('SOON_INCART_LINK', '<B><A HREF="' . DIR_WS_CATALOG . 'contact_us.php">' . SOON_LINK_TEXT . '</A></B> &nbsp;&nbsp; ');
define('SOON_IMAGE_ON','1');
define('SOON_IMAGE','comingsoon_prices.jpg');
 
// SHOW ROOM - PRICE IS SET TO $777777
define('SHOWROOM_TEXT', 'Show Room Only');
define('SHOWROOM_LINK_ON','0');
define('SHOWROOM_LINK_TEXT','Click Here to Contact Us');
define('SHOWROOM_LINK_OFF_TEXT','Retail Outlet Only');
define('SHOWROOM_INCART_LINK', '<B><A HREF="' . DIR_WS_CATALOG . 'contact_us.php">' . SHOWROOM_LINK_TEXT . '</A></B> &nbsp;&nbsp; ');
define('SHOWROOM_IMAGE_ON','1');
define('SHOWROOM_IMAGE','showroom_prices.jpg');

// VISIT OUR OTHER STORE 1 - PRICE IS SET TO $555551
define('VISIT_OTHER1_TEXT', 'Visit Our Other Store 1');
define('VISIT_OTHER1_LINK_ON','1');
define('VISIT_OTHER1_LINK_TEXT','Click Here to Visit Store 1');
define('VISIT_OTHER1_LINK_OFF_TEXT','Visit Our Other Store 1');
define('VISIT_OTHER1_LINK_URL','http://www.webmakers.com');
define('VISIT_OTHER1_LINK_LOGO_ON','1');
define('VISIT_OTHER1_LINK_LOGO_ALT','Visit Other Store 1');
define('VISIT_OTHER1_LINK_LOGO','<IMG SRC="' . DIR_WS_CATALOG . 'images/visitotherstore_logo.jpg"' . 'alt="' . VISIT_OTHER1_LINK_LOGO_ALT . '">');
define('VISIT_OTHER1_IMAGE_ON','1');
define('VISIT_OTHER1_IMAGE','visitotherstore_prices.jpg');

// VISIT OUR OTHER STORE 2 - PRICE IS SET TO $555552
define('VISIT_OTHER2_TEXT', 'Visit Our Other Store 2');
define('VISIT_OTHER2_LINK_ON','1');
define('VISIT_OTHER2_LINK_TEXT','Click Here to Visit Store 2');
define('VISIT_OTHER2_LINK_OFF_TEXT','Visit Our Other Store 2');
define('VISIT_OTHER2_LINK_URL','http://www.power-matrix.com');
define('VISIT_OTHER2_LINK_LOGO_ON','1');
define('VISIT_OTHER2_LINK_LOGO_ALT','Visit Other Store 2');
define('VISIT_OTHER2_LINK_LOGO','<IMG SRC="' . DIR_WS_CATALOG . 'images/visitotherstore_logo.jpg"' . 'alt="' . VISIT_OTHER2_LINK_LOGO_ALT . '">');
define('VISIT_OTHER2_IMAGE_ON','1');
define('VISIT_OTHER2_IMAGE','visitotherstore_prices.jpg');

// VISIT OUR OTHER STORE 3 - PRICE IS SET TO $555553
define('VISIT_OTHER3_TEXT', 'Visit Our Other Store 3');
define('VISIT_OTHER3_LINK_ON','1');
define('VISIT_OTHER3_LINK_TEXT','Click Here to Visit Store 3');
define('VISIT_OTHER3_LINK_OFF_TEXT','Visit Our Other Store 3');
define('VISIT_OTHER3_LINK_URL','http://www.globalonesecurity.com');
define('VISIT_OTHER3_LINK_LOGO_ON','1');
define('VISIT_OTHER3_LINK_LOGO_ALT','Visit Other Store 3');
define('VISIT_OTHER3_LINK_LOGO','<IMG SRC="' . DIR_WS_CATALOG . 'images/visitotherstore_logo.jpg"' . 'alt="' . VISIT_OTHER3_LINK_LOGO_ALT . '">');
define('VISIT_OTHER3_IMAGE_ON','1');
define('VISIT_OTHER3_IMAGE','visitotherstore_prices.jpg');


// STOCK QTY BASED CONTROLS
// Stock at 0
define('SOLD_OUT_ON','0'); // Should Sold Out be used instead of Buy Now and In Cart
define('SOLD_OUT_CODE','<!--//* SOLDOUT *//-->'); // Force a Sold out when qty is > 0
define('SOLD_OUT_TEXT', 'SOLD OUT');
// copy soldout_round.jpg or soldout_square.jpg to soldout.jpg or make your own.
define('SOLD_OUT_IMAGE','soldout.jpg');
define('SOLD_OUT_IMAGE_ON','0'); // Use the Sold Out Image vs Sold Out Text
define('SOLD_OUT_STOCK_SHOWS','0'); // Do not set Product Status to 0 so Sold Out shows and Product Still shows but cannot be purchased
define('ALWAYS_SAY_SOLD_OUT','0'); // Always say Sold Out everywhere
define('ALWAYS_SAY_SOLD_OUT_IMAGE','always_say_sold_out.jpg'); 
// Sold Out Image for above price
// TEXT BASED CONTROLS:

// BLOW OUT PRICES - PRODUCT NAME CONTAINS: <!--//* BOP *//-->
define('BLOW_OUT_CODE','<!--//* BOP *//-->');
define('BLOW_OUT_TEXT', 'BLOW OUT PRICES');
define('BLOW_OUT_IMAGE_ON','1');
define('BLOW_OUT_IMAGE','blowout_prices.jpg');

// TODAY ONLY PRICES - PRODUCT NAME CONTAINS: <!--//* TDOP *//-->
define('TODAY_ONLY_CODE','<!--//* TDOP *//-->');
define('TODAY_ONLY_TEXT', 'TODAY ONLY');
define('TODAY_ONLY_IMAGE_ON','1');
define('TODAY_ONLY_IMAGE','todayonly_prices.jpg');

// LIMITED TIME ONLY PRICES - PRODUCT NAME CONTAINS: <!--//* LTOP *//-->
define('LIMITED_TIME_ONLY_CODE','<!--//* LTOP *//-->');
define('LIMITED_TIME_ONLY_TEXT', 'LIMITED TIME');
define('LIMITED_TIME_ONLY_IMAGE_ON','1');
define('LIMITED_TIME_ONLY_IMAGE','limitedtimeonly_prices.jpg');

// 3 DAYS ONLY PRICES - PRODUCT NAME CONTAINS: <!--//* 3DOP *//-->
define('THREE_DAYS_ONLY_CODE','<!--//* 3DOP *//-->');
define('THREE_DAYS_ONLY_TEXT', '3 DAYS ONLY');
define('THREE_DAYS_ONLY_IMAGE_ON','1');
define('THREE_DAYS_ONLY_IMAGE','3daysonly_prices.jpg');

// EOF: WebMakers.com Added: FREE-CALL FOR PRICE-COMING SOON ETC. v3.0
?>