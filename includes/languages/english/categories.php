<?php
/*
$Id$

osCommerce, Open Source E-Commerce Solutions
http://www.oscommerce.com

Copyright (c) 2013 osCommerce

Released under the GNU General Public License
*/

require('includes/application_top.php');

require('includes/classes/currencies.php');
$currencies = new currencies();
$action     = (isset($_GET['action']) ? $_GET['action'] : '');
function tep_get_models($models_array = '') // Function modified from tep_get_manufacturers()
{
    global $language, $first, $last;
    if (!is_array($models_array))
        $models_array = array();
    $models_query = tep_db_query("SELECT products_id,
	                                     products_model 
                                  FROM " . TABLE_PRODUCTS . " 
                                  ORDER BY products_model");
    $count        = 0;
    while ($models = tep_db_fetch_array($models_query)) {
        if ($count == 0) {
            $first = $models['products_model'];
        }
        $models_array[] = array(
            'id' => $models['products_id'],
            'text' => $models['products_model']
        );
        $count++;
        $last = $models['products_model'];
    }
    
    return $models_array;
}



  if (@tep_not_null($action)) {
    // ULTIMATE Seo Urls 5 PRO by FWR Media
    // If the action will affect the cache entries
    if ( $action == 'insert' || $action == 'update' || $action == 'setflag' ) {
      tep_reset_cache_data_usu5( 'reset' );
    }
    switch ($action) {
        case 'setflag':
            if (($_GET['flag'] == '0') || ($_GET['flag'] == '1')) {
                if (isset($_GET['pID'])) {
                    tep_set_product_status($_GET['pID'], $_GET['flag']);
                }
                
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $_GET['cPath'] . '&pID=' . $_GET['pID']));
            break;
        case 'insert_category':
        case 'update_category':
            $models_array = tep_get_models(); // Mark: for google categpries
            
            if (isset($_POST['categories_id']))
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
            $sort_order                                 = tep_db_prepare_input($_POST['sort_order']);
            $google_category                            = $_POST['google_category'];
            $google_category_baseline                   = $_POST['google_category_baseline'];
            $sql_data_array                             = array(
                'sort_order' => $sort_order
            );
            $sql_data_array['google_category']          = $google_category;
            $sql_data_array['google_category_baseline'] = $google_category_baseline;
            //die(print_r($google_category));
            if ($action == 'insert_category') {
                $insert_sql_data = array(
                    'parent_id' => $current_category_id,
                    'date_added' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array);
                
                $categories_id = tep_db_insert_id();
            } elseif ($action == 'update_category') {
                $update_sql_data = array(
                    'last_modified' => 'now()'
                );
                
                $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                
                tep_db_perform(TABLE_CATEGORIES, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "'");
            }
            
            $languages = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $categories_name_array = $_POST['categories_name'];
                
                $language_id = $languages[$i]['id'];
                
                $sql_data_array = array(
                    'categories_name' => tep_db_prepare_input($categories_name_array[$language_id])
                );
                
                if ($action == 'insert_category') {
                    $insert_sql_data = array(
                        'categories_id' => $categories_id,
                        'language_id' => $languages[$i]['id']
                    );
                    $sql_data_array  = array_merge($sql_data_array, $insert_sql_data);
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array);
                } elseif ($action == 'update_category') {
                    tep_db_perform(TABLE_CATEGORIES_DESCRIPTION, $sql_data_array, 'update', "categories_id = '" . (int) $categories_id . "' and language_id = '" . (int) $languages[$i]['id'] . "'");
                }
            }
            
            $categories_image = new upload('categories_image');
            $categories_image->set_destination(DIR_FS_CATALOG_IMAGES);
            
            if ($categories_image->parse() && $categories_image->save()) {
                tep_db_query("update " . TABLE_CATEGORIES . " set categories_image = '" . tep_db_input($categories_image->filename) . "' where categories_id = '" . (int) $categories_id . "'");
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
            break;
        case 'delete_category_confirm':
            if (isset($_POST['categories_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                
                $categories      = tep_get_category_tree($categories_id, '', '0', '', true);
                $products        = array();
                $products_delete = array();
                
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    $product_ids_query = tep_db_query("select products_id from " . TABLE_PRODUCTS_TO_CATEGORIES . " where categories_id = '" . (int) $categories[$i]['id'] . "'");
                    
                    while ($product_ids = tep_db_fetch_array($product_ids_query)) {
                        $products[$product_ids['products_id']]['categories'][] = $categories[$i]['id'];
                    }
                }
                
                reset($products);
                foreach ($products as $key => $value) {
                    $category_ids = '';
                    
                    for ($i = 0, $n = sizeof($value['categories']); $i < $n; $i++) {
                        $category_ids .= "'" . (int) $value['categories'][$i] . "', ";
                    }
                    $category_ids = substr($category_ids, 0, -2);
                    
                    $check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $key . "' and categories_id not as (" . $category_ids . ")");
                    $check       = tep_db_fetch_array($check_query);
                    if ($check['total'] < '1') {
                        $products_delete[$key] = $key;
                    }
                }
                
                // removing categories can be a lengthy process
                tep_set_time_limit(0);
                for ($i = 0, $n = sizeof($categories); $i < $n; $i++) {
                    tep_remove_category($categories[$i]['id']);
                }
                
                reset($products_delete);
                while (list($key) = each($products_delete)) {
                    tep_remove_product($key);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'delete_product_confirm':
            if (isset($_POST['products_id']) && isset($_POST['product_categories']) && is_array($_POST['product_categories'])) {
                $product_id         = tep_db_prepare_input($_POST['products_id']);
                $product_categories = $_POST['product_categories'];
                
                for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
                    tep_db_query("delete from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $product_id . "' and categories_id = '" . (int) $product_categories[$i] . "'");
                }
                
                $product_categories_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $product_id . "'");
                $product_categories       = tep_db_fetch_array($product_categories_query);
                
                if ($product_categories['total'] == '0') {
                    tep_remove_product($product_id);
                }
            }
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath));
            break;
        case 'move_category_confirm':
            if (isset($_POST['categories_id']) && ($_POST['categories_id'] != $_POST['move_to_category_id'])) {
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
                
                $path = explode('_', tep_get_generated_category_path_ids($new_parent_id));
                
                if (in_array($categories_id, $path)) {
                    $messageStack->add_session(ERROR_CANNOT_MOVE_CATEGORY_TO_PARENT, 'error');
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories_id));
                } else {
                    tep_db_query("update " . TABLE_CATEGORIES . " set parent_id = '" . (int) $new_parent_id . "', last_modified = now() where categories_id = '" . (int) $categories_id . "'");
                    
                    if (USE_CACHE == 'true') {
                        tep_reset_cache_block('categories');
                        tep_reset_cache_block('also_purchased');
                    }
                    
                    tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&cID=' . $categories_id));
                }
            }
            
            break;
        case 'move_product_confirm':
            $products_id   = tep_db_prepare_input($_POST['products_id']);
            $new_parent_id = tep_db_prepare_input($_POST['move_to_category_id']);
            
            $duplicate_check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $new_parent_id . "'");
            $duplicate_check       = tep_db_fetch_array($duplicate_check_query);
            if ($duplicate_check['total'] < 1)
                tep_db_query("update " . TABLE_PRODUCTS_TO_CATEGORIES . " set categories_id = '" . (int) $new_parent_id . "' where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $current_category_id . "'");
            
            if (USE_CACHE == 'true') {
                tep_reset_cache_block('categories');
                tep_reset_cache_block('also_purchased');
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $new_parent_id . '&pID=' . $products_id));
            break;
        case 'insert_product':
        case 'update_product':
            if (isset($_POST['edit_x']) || isset($_POST['edit_y'])) {
                $action = 'new_product';
            } else {
                if (isset($_GET['pID']))
                    $products_id = tep_db_prepare_input($_GET['pID']);
                $products_date_available = tep_db_prepare_input($_POST['products_date_available']);
                
                $products_date_available = (date('Y-m-d') < $products_date_available) ? $products_date_available : 'null';
                
                $sql_data_array = array(
                    'products_quantity' => (int) tep_db_prepare_input($_POST['products_quantity']),
                    'products_model' => tep_db_prepare_input($_POST['products_model']),
                    'products_price' => tep_db_prepare_input($_POST['products_price']),
                    'products_date_available' => $products_date_available,
                    'products_weight' => (float) tep_db_prepare_input($_POST['products_weight']),
                    'products_status' => tep_db_prepare_input($_POST['products_status']),
                    'products_tax_class_id' => tep_db_prepare_input($_POST['products_tax_class_id']),
                    'manufacturers_id' => (int) tep_db_prepare_input($_POST['manufacturers_id'])
                );
                
                $products_image = new upload('products_image');
                $products_image->set_destination(DIR_FS_CATALOG_IMAGES);
                if ($products_image->parse() && $products_image->save()) {
                    $sql_data_array['products_image'] = tep_db_prepare_input($products_image->filename);
                }
                
                if ($action == 'insert_product') {
                    $insert_sql_data = array(
                        'products_date_added' => 'now()'
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                    
                    tep_db_perform(TABLE_PRODUCTS, $sql_data_array);
                    $products_id = tep_db_insert_id();
                    
                    tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $current_category_id . "')");
                } elseif ($action == 'update_product') {
                    $update_sql_data = array(
                        'products_last_modified' => 'now()'
                    );
                    
                    $sql_data_array = array_merge($sql_data_array, $update_sql_data);
                    
                    tep_db_perform(TABLE_PRODUCTS, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "'");
                }
                
                $languages = tep_get_languages();
                for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                    $language_id = $languages[$i]['id'];                    
					$productURL = str_replace(array('http://','https://'),'',$_POST['products_url'][$language_id]);
					$productVideoURL = str_replace(array('http://','https://'),'',$_POST['products_video_url'][$language_id]);
                    $sql_data_array = array(
                        'products_name' => tep_db_prepare_input($_POST['products_name'][$language_id]),
                        'products_description' => tep_db_prepare_input($_POST['products_description'][$language_id]),
						'products_description_mini' => tep_db_prepare_input($_POST['products_description_mini'][$language_id]),
                        'products_url' => tep_db_prepare_input($productURL),
						'products_video_url' => tep_db_prepare_input($productVideoURL)
                    );
                    
                    if ($action == 'insert_product') {
                        $insert_sql_data = array(
                            'products_id' => $products_id,
                            'language_id' => $language_id
                        );
                        
                        $sql_data_array = array_merge($sql_data_array, $insert_sql_data);
                        
                        tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array);
                    } elseif ($action == 'update_product') {
                        tep_db_perform(TABLE_PRODUCTS_DESCRIPTION, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and language_id = '" . (int) $language_id . "'");
                    }
                }
                // start indvship
                $tmp_products_ship_price = round(tep_db_prepare_input($_POST['products_ship_price']), 4);
                if ($tmp_products_ship_price == 0) {
                    $tmp_products_ship_price = null;
                }
                ;
                $tmp_products_ship_price_two = round(tep_db_prepare_input($_POST['products_ship_price_two']), 4);
                if ($tmp_products_ship_price_two == 0) {
                    $tmp_products_ship_price_two = null;
                }
                ;
                
                
                $pi_sort_order = 0;
                $piArray       = array(
                    0
                );
                
                foreach ($HTTP_POST_FILES as $key => $value) {
                    // Update existing large product images
                    if (preg_match('/^products_image_large_([0-9]+)$/', $key, $matches)) {
                        $pi_sort_order++;
                        
                        $sql_data_array = array(
                            'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_' . $matches[1]]),
                            'sort_order' => $pi_sort_order
                        );
                        
                        $t = new upload($key);
                        $t->set_destination(DIR_FS_CATALOG_IMAGES);
                        if ($t->parse() && $t->save()) {
                            $sql_data_array['image'] = tep_db_prepare_input($t->filename);
                        }
                        
                        tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array, 'update', "products_id = '" . (int) $products_id . "' and id = '" . (int) $matches[1] . "'");
                        
                        $piArray[] = (int) $matches[1];
                    } elseif (preg_match('/^products_image_large_new_([0-9]+)$/', $key, $matches)) {
                        // Insert new large product images
                        $sql_data_array = array(
                            'products_id' => (int) $products_id,
                            'htmlcontent' => tep_db_prepare_input($_POST['products_image_htmlcontent_new_' . $matches[1]])
                        );
                        
                        $t = new upload($key);
                        $t->set_destination(DIR_FS_CATALOG_IMAGES);
                        if ($t->parse() && $t->save()) {
                            $pi_sort_order++;
                            
                            $sql_data_array['image']      = tep_db_prepare_input($t->filename);
                            $sql_data_array['sort_order'] = $pi_sort_order;
                            
                            tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array);
                            
                            $piArray[] = tep_db_insert_id();
                        }
                    }
                }
                
                $product_images_query = tep_db_query("select image from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int) $products_id . "' and id not as (" . implode(',', $piArray) . ")");
                if (tep_db_num_rows($product_images_query)) {
                    while ($product_images = tep_db_fetch_array($product_images_query)) {
                        $duplicate_image_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_IMAGES . " where image = '" . tep_db_input($product_images['image']) . "'");
                        $duplicate_image       = tep_db_fetch_array($duplicate_image_query);
                        
                        if ($duplicate_image['total'] < 2) {
                            if (file_exists(DIR_FS_CATALOG_IMAGES . $product_images['image'])) {
                                @unlink(DIR_FS_CATALOG_IMAGES . $product_images['image']);
                            }
                        }
                    }
                    
                    tep_db_query("delete from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int) $products_id . "' and id not in (" . implode(',', $piArray) . ")");
                }
                //indvship
                $sql_shipping_array    = array(
                    'products_ship_zip' => tep_db_prepare_input($_POST['products_ship_zip']),
                    'products_ship_methods_id' => tep_db_prepare_input($_POST['products_ship_methods_id']),
                    'products_ship_price' => $tmp_products_ship_price,
                    'products_ship_price_two' => $tmp_products_ship_price_two,
                    'products_ship_exempt_free' => tep_db_prepare_input($_POST['products_ship_exempt_free'])
                );
                $sql_shipping_id_array = array(
                    'products_id' => (int) $products_id
                );
                $products_ship_query   = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS_SHIPPING . " WHERE products_id = " . (int) $products_id);
                if (tep_db_num_rows($products_ship_query) > 0) {
                    if (($_POST['products_ship_zip'] == '') && ($_POST['products_ship_methods_id'] == '') && ($_POST['products_ship_price'] == '') && ($_POST['products_ship_price_two'] == '') && ($_POST['products_ship_exempt_free'] == '')) {
                        tep_db_query("DELETE FROM " . TABLE_PRODUCTS_SHIPPING . " where products_id = '" . (int) $products_id . "'");
                    } else {
                        tep_db_perform(TABLE_PRODUCTS_SHIPPING, $sql_shipping_array, 'update', "products_id = '" . (int) $products_id . "'");
                    }
                } else {
                    if (($_POST['products_ship_zip'] != '') || ($_POST['products_ship_methods_id'] != '') || ($_POST['products_ship_price'] != '') || ($_POST['products_ship_price_two'] != '') || ($_POST['products_ship_exempt_free'] != '')) {
                        $sql_ship_array = array_merge($sql_shipping_array, $sql_shipping_id_array);
                        tep_db_perform(TABLE_PRODUCTS_SHIPPING, $sql_ship_array, 'insert');
                    }
                }
                // end indvship
                
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
                
                tep_redirect(tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products_id));
            }
            break;
        case 'copy_to_confirm':
            if (isset($_POST['products_id']) && isset($_POST['categories_id'])) {
                $products_id   = tep_db_prepare_input($_POST['products_id']);
                $categories_id = tep_db_prepare_input($_POST['categories_id']);
                
                if ($_POST['copy_as'] == 'link') {
                    if ($categories_id != $current_category_id) {
                        $check_query = tep_db_query("select count(*) as total from " . TABLE_PRODUCTS_TO_CATEGORIES . " where products_id = '" . (int) $products_id . "' and categories_id = '" . (int) $categories_id . "'");
                        $check       = tep_db_fetch_array($check_query);
                        if ($check['total'] < '1') {
                            tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . (int) $products_id . "', '" . (int) $categories_id . "')");
                        }
                    } else {
                        $messageStack->add_session(ERROR_CANNOT_LINK_TO_SAME_CATEGORY, 'error');
                    }
                } elseif ($_POST['copy_as'] == 'duplicate') {
                    
                    // $product_query = tep_db_query("select products_quantity, products_model, products_image, products_price, products_date_available, products_weight, products_tax_class_id, manufacturers_id from " . TABLE_PRODUCTS . " where products_id = '" . (int)$products_id . "'");
                    // $product = tep_db_fetch_array($product_query);
                    
                    // tep_db_query("insert into " . TABLE_PRODUCTS . " (products_quantity, products_model,products_image, products_price, products_date_added, products_date_available, products_weight, products_status, products_tax_class_id, manufacturers_id) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_price']) . "',  now(), '" . tep_db_input($product['products_date_available']) . "', '" . tep_db_input($product['products_weight']) . "', '0', '" . (int)$product['products_tax_class_id'] . "', '" . (int)$product['manufacturers_id'] . "')");
                    // $dup_products_id = tep_db_insert_id();
                    
                    //PRODUCT_SORT_ORDER ADDED TO THIS SELECT
                    $product_query = tep_db_query("select products_quantity, products_model, products_image, products_price, products_date_available, products_weight, products_tax_class_id, manufacturers_id, products_sort_order from " . TABLE_PRODUCTS . " where products_id = '" . (int) $products_id . "' ORDER BY products_sort_order");
                    $product       = tep_db_fetch_array($product_query);
                    
                    //PRODUCT_SORT_ORDER ADDED TO THIS INSERT
                    tep_db_query("insert into " . TABLE_PRODUCTS . " (products_quantity, products_model,products_image, products_price, products_date_added, products_date_available, products_weight, products_status, products_tax_class_id, manufacturers_id) values ('" . tep_db_input($product['products_quantity']) . "', '" . tep_db_input($product['products_model']) . "', '" . tep_db_input($product['products_image']) . "', '" . tep_db_input($product['products_price']) . "',  now(), " . (empty($product['products_date_available']) ? "null" : "'" . tep_db_input($product['products_date_available']) . "'") . ", '" . tep_db_input($product['products_weight']) . "', '0', '" . (int) $product['products_tax_class_id'] . "', '" . (int) $product['manufacturers_id'] . "', '" . (int) $product['products_sort_order'] . "')");
                    $dup_products_id = tep_db_insert_id();
                    
                    //EOF Product sort o
                    
                    $description_query = tep_db_query("select language_id, products_name, products_description, products_description_mini, products_url, products_video_url from " . TABLE_PRODUCTS_DESCRIPTION . " where products_id = '" . (int) $products_id . "'");
                    while ($description = tep_db_fetch_array($description_query)) {
                        tep_db_query("insert into " . TABLE_PRODUCTS_DESCRIPTION . " (products_id, language_id, products_name, products_description, products_description_mini, products_url, products_video_url, products_viewed) values ('" . (int) $dup_products_id . "', '" . (int) $description['language_id'] . "', '" . tep_db_input($description['products_name']) . "', '" . tep_db_input($description['products_description']) . "', '" . tep_db_input($description['products_url']) . "', '" . tep_db_input($description['products_video_url']) . "', '0')");
                    }
                    
                    // start indvship
                    $shipping_query = tep_db_query("select products_ship_methods_id, products_ship_zip from " . TABLE_PRODUCTS_SHIPPING . " where products_id = '" . (int) $products_id . "'");
                    while ($shipping = tep_db_fetch_array($shipping_query)) {
                        tep_db_query("insert into " . TABLE_PRODUCTS_SHIPPING . " (products_id, products_ship_methods_id, products_ship_zip) values ('" . (int) $dup_products_id . "', '" . tep_db_input($shipping['products_ship_methods_id']) . "', '" . tep_db_input($shipping['products_ship_zip']) . "')");
                    }
                    // end indvship
                    
                    tep_db_query("insert into " . TABLE_PRODUCTS_TO_CATEGORIES . " (products_id, categories_id) values ('" . (int) $dup_products_id . "', '" . (int) $categories_id . "')");
                    $products_id = $dup_products_id;
                }
                
                if (USE_CACHE == 'true') {
                    tep_reset_cache_block('categories');
                    tep_reset_cache_block('also_purchased');
                }
            }
            
            tep_redirect(tep_href_link('categories.php', 'cPath=' . $categories_id . '&pID=' . $products_id));
            break;
    }
}

// check if the catalog image directory exists
if (is_dir(DIR_FS_CATALOG_IMAGES)) {
    if (!tep_is_writable(DIR_FS_CATALOG_IMAGES))
        $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_NOT_WRITEABLE, 'error');
} else {
    $messageStack->add(ERROR_CATALOG_IMAGE_DIRECTORY_DOES_NOT_EXIST, 'error');
}

require('includes/template_top.php');


// ---- Mark
if ($action == 'editContent_category') {
    if (isset($_GET['cID']) && empty($_POST)) {
        $catID            = $_GET['cID'];
        $categories_query = tep_db_query("select * from " . TABLE_CATEGORIES_CONTENT . " cc where cc.categories_id = " . $catID . " limit 1");
        $categoryContent  = tep_db_fetch_array($categories_query);
        if ($categoryContent) {
            $formAction = 'update_category_content';
            $theContent = $categoryContent['categories_content'];
        } else {
            $formAction = 'new_category_content';
            $theContent = '';
        }
        
?>
		<table border="0" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php
        echo tep_get_category_name($catID, 1);
?> Category Content</td>
            <td class="pageHeading" align="right"><?php
        echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT);
?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td>
		<?
        echo tep_draw_form($editContent_category, 'categories.php', '&cID=' . $_GET['cID'] . '&action=editContent_category_preview', 'post', 'enctype="multipart/form-data"');
?> <textarea name='categoryContent'  wrap='soft' cols='100' rows='50'><?php
        echo $theContent;
?></textarea>      </td></tr>
			
		<tr>
			<td class="main" align="left">
			<?php
        echo tep_draw_hidden_field('cID', $catID) . tep_draw_hidden_field('formAction', $formAction) . tep_image_submit('button_preview.gif', IMAGE_PREVIEW);
?></td><td class="main" align="right">
			<?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>';
?>
			<td class="main" align="right">
		</td>
          </tr>
	  </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>

    </table></form><?php
    }
} else if ($action == 'editContent_category_preview') {
    //	echo 'cID: ' . $_GET['cID'];
    if (isset($_GET['cID'])) {
        
        $catID      = $_POST['cID'];
        $formAction = $_POST['action'];
        $theContent = $_POST['categoryContent'];
        //echo 'catID: ' . $catID . '<br />' . 'formAction: ' . $formAction . '<br />' . '$theContent: ' . $theContent;
?>
		<table border="0" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php
        echo tep_get_category_name($catID, 1);
?> Category Content Preview</td>
            <td class="pageHeading" align="right"><?php
        echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT);
?></td>
          </tr>
        </table>
		</td>
      </tr>
      <tr>
        <td  >
			<table border="0" width="100%" cellspacing="25" cellpadding="25">
          <tr>
            <td class="main" style="border: 1px solid #000"><?php
        echo $theContent;
?></td>
          </tr>
        </table>
		</td>
      </tr>
      <tr>
        <td>
		<?
        echo tep_draw_form($formAction, 'categories.php', '&cID=' . $_GET['cID'] . '&action=' . $formAction, 'post', 'enctype="multipart/form-data"');
?> <textarea name='categoryContent'  wrap='soft' cols='100' rows='50'><?php
        echo $theContent;
?></textarea></td></tr>
		<tr>
			<td class="main" align="left"><?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_back.gif', IMAGE_BACK) . '</a>';
?></td><td><?php
        echo tep_draw_hidden_field('cID', $catID) . tep_image_submit('button_update.gif', IMAGE_UPDATE);
?></td><td class="main" align="right">
			<?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')) . '">' . tep_image_button('button_cancel.gif', IMAGE_CANCEL) . '</a>';
?>
          </tr>
	  </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>

    </table></form><?php
    }
    /// ------------ end mark
} else if ($action == 'new_product') {
    
    if ($action == 'new_product') {
        $parameters = array(
            'products_name' => '',
            'products_description' => '',
			'products_description_mini' => '',
            'products_url' => '',
			'products_video_url' => '',
            'products_id' => '',
            'products_quantity' => '',
            'products_model' => '',
            'products_image' => '',
            'products_larger_images' => array(),
            'products_price' => '',
            'products_weight' => '',
            'products_date_added' => '',
            'products_last_modified' => '',
            'products_date_available' => '',
            'products_status' => '',
            'products_tax_class_id' => '',
            'manufacturers_id' => '',
            'products_sort_order' => ''
        );
        
        
        $pInfo = new objectInfo($parameters);
        
        if (isset($_GET['pID']) && empty($_POST)) {
            $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_description_mini, pd.products_url, pd.products_video_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . (int) $_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "'");
            $product       = tep_db_fetch_array($product_query);
            
            $pInfo->objectInfo($product);
            // start indvship
            $products_shipping_query = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS_SHIPPING . " WHERE products_id=" . (int) $_GET['pID']);
            while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
                $products_ship_zip         = $products_shipping['products_ship_zip'];
                $products_ship_methods_id  = $products_shipping['products_ship_methods_id'];
                $products_ship_price       = $products_shipping['products_ship_price'];
                $products_ship_price_two   = $products_shipping['products_ship_price_two'];
                $products_ship_exempt_free = $products_shipping['products_ship_exempt_free'];
            }
            $shipping = array(
                'products_ship_methods_id' => $products_ship_methods_id,
                'products_ship_zip' => $products_ship_zip,
                'products_ship_price' => $products_ship_price,
                'products_ship_price_two' => $products_ship_price_two,
                'products_ship_exempt_free' => $products_ship_exempt_free
            );
            $pInfo->objectInfo($shipping);
            // end indvship
            
            
            if (!isset($pInfo->products_status))
                $pInfo->products_status = '1';
            
            switch ($shipping->products_ship_exempt_free) {
                case '1':
                    $products_ship_exempt_free_yes = true;
                    $products_ship_exempt_free_no  = false;
                    break;
                case '0':
                default:
                    $products_ship_exempt_free_yes = false;
                    $products_ship_exempt_free_no  = true;
            }
            $product_images_query = tep_db_query("select id, image, htmlcontent, sort_order from " . TABLE_PRODUCTS_IMAGES . " where products_id = '" . (int) $product['products_id'] . "' order by sort_order");
            while ($product_images = tep_db_fetch_array($product_images_query)) {
                $pInfo->products_larger_images[] = array(
                    'id' => $product_images['id'],
                    'image' => $product_images['image'],
                    'htmlcontent' => $product_images['htmlcontent'],
                    'sort_order' => $product_images['sort_order']
                );
            } // end indvship
            $product_query = tep_db_query("select pd.products_name, pd.products_description, pd.products_description_mini, pd.products_url, pd.products_video_url, p.products_id, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_sort_order ,p.products_date_added, p.products_last_modified, date_format(p.products_date_available, '%Y-%m-%d') as products_date_available, p.products_status, p.products_tax_class_id, p.manufacturers_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = '" . (int) $_GET['pID'] . "' and p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "'");
            $product       = tep_db_fetch_array($product_query);
            
            $pInfo->objectInfo($product);
        } elseif (@tep_not_null($_POST)) {
            $pInfo->objectInfo($_POST);
            $products_name        = $_POST['products_name'];
            $products_description = $_POST['products_description'];
			$products_description_mini = $_POST['products_description_mini'];
            $products_url         = $_POST['products_url'];
			$products_video_url         = $_POST['products_video_url'];
        }
    }
    
    $manufacturers_array = array(
        array(
            'id' => '',
            'text' => TEXT_NONE
        )
    );
    $manufacturers_query = tep_db_query("select manufacturers_id, manufacturers_name from " . TABLE_MANUFACTURERS . " order by manufacturers_name");
    while ($manufacturers = tep_db_fetch_array($manufacturers_query)) {
        $manufacturers_array[] = array(
            'id' => $manufacturers['manufacturers_id'],
            'text' => $manufacturers['manufacturers_name']
        );
    }
    
    $tax_class_array = array(
        array(
            'id' => '0',
            'text' => TEXT_NONE
        )
    );
    $tax_class_query = tep_db_query("select tax_class_id, tax_class_title from " . TABLE_TAX_CLASS . " order by tax_class_title");
    while ($tax_class = tep_db_fetch_array($tax_class_query)) {
        $tax_class_array[] = array(
            'id' => $tax_class['tax_class_id'],
            'text' => $tax_class['tax_class_title']
        );
    }
    
    $languages = tep_get_languages();
    
    if (!isset($pInfo->products_status))
        $pInfo->products_status = '1';
    switch ($pInfo->products_status) {
        case '0':
            $in_status  = false;
            $out_status = true;
            break;
        case '1':
        default:
            $in_status  = true;
            $out_status = false;
    }
    
    $form_action = (isset($_GET['pID'])) ? 'update_product' : 'insert_product';
?>
<script type="text/javascript"><!--
var tax_rates = new Array();
<?php
    for ($i = 0, $n = sizeof($tax_class_array); $i < $n; $i++) {
        if ($tax_class_array[$i]['id'] > 0) {
            echo 'tax_rates["' . $tax_class_array[$i]['id'] . '"] = ' . tep_get_tax_rate_value($tax_class_array[$i]['id']) . ';' . "\n";
        }
    }
?>

function doRound(x, places) {
  return Math.round(x * Math.pow(10, places)) / Math.pow(10, places);
}

function getTaxRate() {
  var selected_value = document.forms["new_product"].products_tax_class_id.selectedIndex;
  var parameterVal = document.forms["new_product"].products_tax_class_id[selected_value].value;

  if ( (parameterVal > 0) && (tax_rates[parameterVal] > 0) ) {
    return tax_rates[parameterVal];
  } else {
    return 0;
  }
}

function updateGross() {
  var taxRate = getTaxRate();
  var grossValue = document.forms["new_product"].products_price.value;

  if (taxRate > 0) {
    grossValue = grossValue * ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price_gross.value = doRound(grossValue, 4);
}

function updateNet() {
  var taxRate = getTaxRate();
  var netValue = document.forms["new_product"].products_price_gross.value;

  if (taxRate > 0) {
    netValue = netValue / ((taxRate / 100) + 1);
  }

  document.forms["new_product"].products_price.value = doRound(netValue, 4);
}
//--></script>
    <?php
    echo tep_draw_form('new_product', 'categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '') . '&action=' . $form_action, 'post', 'enctype="multipart/form-data"');
?>
    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php
    echo sprintf(TEXT_NEW_PRODUCT, tep_output_generated_category_path($current_category_id));
?></td>
            <td class="pageHeading" align="right"><?php
    echo tep_draw_separator('pixel_trans.gif', HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT);
?></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
      <tr>
        <td><table border="0" cellspacing="0" cellpadding="2">
		<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
?>
          <tr>
            <td class="main"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_NAME;
?></td>
            <td class="main" colspan=4><?php
        echo  tep_draw_input_field('products_name[' . $languages[$i]['id'] . ']', (empty($pInfo->products_id) ? '' : tep_get_products_name($pInfo->products_id, $languages[$i]['id'])),'size="100" ') . '&nbsp;' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);
?></td>
          </tr>
<?php
    }
?>
		
		
		
		
		
		
		
		
		
		
		
         
          <tr>
            <td class="main"><?php
    echo TEXT_PRODUCTS_MANUFACTURER;
?></td>
            <td class="main"><?php
    echo tep_draw_pull_down_menu('manufacturers_id', $manufacturers_array, $pInfo->manufacturers_id);
?></td>
        
            <td class="main"><?php
    echo TEXT_PRODUCTS_MODEL;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_model', $pInfo->products_model);
?></td>
          </tr>
          

          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
		  
		  <?php // start indvship 
?> <!-- Zipcode -->
		  
		  
          <tr>
            <td class="main">Group</td>
            <td class="main"><?php
    echo tep_draw_input_field('products_ship_zip', $pInfo->products_ship_zip);
    if (@tep_not_null($pInfo->products_ship_zip))
        echo 'notnull';
    else
        echo 'null';
?></td><td class="main">Current Keywords:</td>
			<td rowspan=3 class="main"> 
			<?php
   
    $select = tep_db_query("SELECT * FROM `products_shipping` GROUP BY `products_ship_zip` ORDER BY `products_ship_zip` DESC");
    while ($keywords = tep_db_fetch_array($select)) {
        echo "<a href='javascript:null();' onclick=\"document.forms['new_product'].products_ship_zip.value='" . $keywords['products_ship_zip'] . "';document.forms['new_product'].products_ship_price.value='" . $keywords['products_ship_price'] . "';document.forms['new_product'].products_ship_price_two.value='" . $keywords['products_ship_price_two'] . "';\">" . $keywords['products_ship_zip'] . "<br />";
        
    }
?></td>
		  </tr> <!-- end Zipcode --> <!-- Indvship -->
		  
          <tr >
            <td class="main"><?php
    echo 'Indv. Shipping Price:';
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_ship_price', $pInfo->products_ship_price);
    if (@tep_not_null($pInfo->products_ship_price))
        echo 'notnull';
    else
        echo 'null';
?></td>
          </tr>
          <tr>
            <td class="main"><?php
    echo 'Qty to qualify for free shipping:';
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_ship_price_two', $pInfo->products_ship_price_two);
    if (@tep_not_null($pInfo->products_ship_price_two))
        echo 'notnull';
    else
        echo 'null';
?></td>
          </tr>
		  <tr>
			 <td class="main"><?php
    echo 'Exempt free shipping > £200?:';
?></td>
			 <td class="main"><?php
    echo tep_draw_separator('pixel_trans.gif', '24', '15') . '&nbsp;No:&nbsp;' . tep_draw_radio_field('products_ship_exempt_free', '0', $products_ship_exempt_free_no) . '&nbsp;Yes:&nbsp;' . tep_draw_radio_field('products_ship_exempt_free', '1', $products_ship_exempt_free_yes);
?></td>
        <!-- end Indvship -->
<td rowspan=2 class="main" valign="top"><?php echo TEXT_PRODUCTS_IMAGE;?></td>
            <td rowspan=2 class="main"><div><?php echo '<strong>' . TEXT_PRODUCTS_MAIN_IMAGE . ' <small>(' . SMALL_IMAGE_WIDTH . ' x ' . SMALL_IMAGE_HEIGHT . 'px)</small></strong><br />' . (@tep_not_null($pInfo->products_image) ? '<a href="' . DIR_WS_CATALOG_IMAGES . $pInfo->products_image . '" target="_blank">' . $pInfo->products_image . '</a> &#124; ' : '') . tep_draw_file_field('products_image');
?></div>

              <ul id="piList">
<?php
    $pi_counter = 0;
    
    foreach ($pInfo->products_larger_images as $pi) {
        $pi_counter++;
        
        echo '                <li id="piId' . $pi_counter . '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' . $pi_counter . ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong>' . TEXT_PRODUCTS_LARGE_IMAGE . '</strong><br />' . tep_draw_file_field('products_image_large_' . $pi['id']) . '<br /><a href="' . DIR_WS_CATALOG_IMAGES . $pi['image'] . '" target="_blank">' . $pi['image'] . '</a><br /><br />' . TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT . '<br />' . tep_draw_textarea_field('products_image_htmlcontent_' . $pi['id'], 'soft', '70', '3', $pi['htmlcontent']) . '</li>';
    }
?>
              </ul>

              <a href="#" onclick="addNewPiForm();return false;"><span class="ui-icon ui-icon-plus" style="float: left;"></span><?php
    echo TEXT_PRODUCTS_ADD_LARGE_IMAGE;
?></a>

<div id="piDelConfirm" title="<?php
    echo TEXT_PRODUCTS_LARGE_IMAGE_DELETE_TITLE;
?>">
  <p><span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span><?php
    echo TEXT_PRODUCTS_LARGE_IMAGE_CONFIRM_DELETE;
?></p>
</div>

<style type="text/css">
#piList { list-style-type: none; margin: 0; padding: 0; }
#piList li { margin: 5px 0; padding: 2px; }
</style>

<script type="text/javascript">
$('#piList').sortable({
  containment: 'parent'
});

var piSize = <?php
    echo $pi_counter;
?>;

function addNewPiForm() {
  piSize++;

  $('#piList').append('<li id="piId' + piSize + '" class="ui-state-default"><span class="ui-icon ui-icon-arrowthick-2-n-s" style="float: right;"></span><a href="#" onclick="showPiDelConfirm(' + piSize + ');return false;" class="ui-icon ui-icon-trash" style="float: right;"></a><strong><?php
    echo TEXT_PRODUCTS_LARGE_IMAGE;
?></strong><br /><input type="file" name="products_image_large_new_' + piSize + '" /><br /><br /><?php
    echo TEXT_PRODUCTS_LARGE_IMAGE_HTML_CONTENT;
?><br /><textarea name="products_image_htmlcontent_new_' + piSize + '" wrap="soft" cols="70" rows="3"></textarea></li>');
}

var piDelConfirmId = 0;

$('#piDelConfirm').dialog({
  autoOpen: false,
  resizable: false,
  draggable: false,
  modal: true,
  buttons: {
    'Delete': function() {
      $('#piId' + piDelConfirmId).effect('blind').remove();
      $(this).dialog('close');
    },
    Cancel: function() {
      $(this).dialog('close');
    }
  }
});

function showPiDelConfirm(piId) {
  piDelConfirmId = piId;

  $('#piDelConfirm').dialog('open');
}
</script>

            </td>
          </tr>
		  
		  <?php
    // end indvship 
    if (isset($pInfo->products_tax_class_id)) {
        $taxSelection = $pInfo->products_tax_class_id;
    } else {
        $taxSelection = 1;
    }
?>
				
          <tr >
            <td class="main"><?php
    echo TEXT_PRODUCTS_TAX_CLASS;
?></td>
            <td class="main">
			<?php echo tep_draw_pull_down_menu('products_tax_class_id', $tax_class_array, $pInfo->products_tax_class_id, 'onchange="updateGross()"');?></td> 
            
          </tr>
          <tr >
            <td class="main"><?php
    echo TEXT_PRODUCTS_PRICE_NET;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_price', $pInfo->products_price, 'onkeyup="updateGross()"');
?></td>
          <tr >
            <td class="main"><?php
    echo TEXT_PRODUCTS_PRICE_GROSS;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_price_gross', $pInfo->products_price, 'onkeyup="updateNet()"');
?></td>
          </tr><tr>
<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
?>
          <tr>
            <td class="main"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_VIDEO_URL;
?></td>
            <td class="main" colspan=3><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_video_url[' . $languages[$i]['id'] . ']', (isset($products_video_url[$languages[$i]['id']]) ? stripslashes($products_video_url[$languages[$i]['id']]) : tep_get_products_video_url($pInfo->products_id, $languages[$i]['id'])),'size="100" ');
?></td>
          </tr>
<?php
    }
?>
<script type="text/javascript"><!--
updateGross();
//--></script>
<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
?>
          <tr>
            <td class="main" valign="top"><?php  echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);

        if ($i == 0)
            echo TEXT_PRODUCTS_DESCRIPTION_MINI;
?></td></tr><tr>
            <td colspan=4><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top">
      </td>
                <td class="main"><?php
        echo tep_draw_textarea_field('products_description_mini[' . $languages[$i]['id'] . ']', 'soft', '120', '1', (empty($pInfo->products_id) ? '' : stripslashes(tep_get_products_description_mini($pInfo->products_id, $languages[$i]['id']))));
?></td>
              </tr>
            </table></td>
          </tr>
<?php
    }
?>

<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
?>
          <tr>
            <td class="main" valign="top"><?php  echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']);

        if ($i == 0)
            echo TEXT_PRODUCTS_DESCRIPTION;
?></td></tr><tr>
            <td colspan=4><table border="0" cellspacing="0" cellpadding="0">
              <tr>
                <td class="main" valign="top">
      </td>
                <td class="main"><?php
        echo tep_draw_textarea_field('products_description[' . $languages[$i]['id'] . ']', 'soft', '120', '40', (empty($pInfo->products_id) ? '' : stripslashes(tep_get_products_description($pInfo->products_id, $languages[$i]['id']))));
?></td>
              </tr>
            </table></td>
          </tr>
<?php
    }
?>
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
          <tr>
            <td class="main"><?php
    echo TEXT_PRODUCTS_QUANTITY;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_quantity', $pInfo->products_quantity);
?></td>
          </tr>
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
         
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
         
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
<?php
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
?>
          <tr>
            <td class="main"><?php
        if ($i == 0)
            echo TEXT_PRODUCTS_URL;
?></td>
            <td class="main" colspan=3><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('products_url[' . $languages[$i]['id'] . ']', (isset($products_url[$languages[$i]['id']]) ? stripslashes($products_url[$languages[$i]['id']]) : tep_get_products_url($pInfo->products_id, $languages[$i]['id'])),'size="100" ');
?></td>
          </tr>
<?php
    }
?>
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
          <tr>
            <td class="main"><?php
    echo TEXT_PRODUCTS_WEIGHT;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_weight', $pInfo->products_weight);
?></td>
          </tr>



	    <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
          <tr>
            <td class="main"><?php
    echo TEXT_EDIT_SORT_ORDER;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_sort_order', $pInfo->products_sort_order, 'size="2"');
?></td>
          </tr>
			 <tr>
            <td class="main">
			
			
			
			<?php
    echo TEXT_PRODUCTS_STATUS;
?></td>
            <td class="main"><?php
    echo tep_draw_radio_field('products_status', '1', $in_status) . '&nbsp;' . TEXT_PRODUCT_AVAILABLE . '&nbsp;' . tep_draw_radio_field('products_status', '0', $out_status) . '&nbsp;' . TEXT_PRODUCT_NOT_AVAILABLE;
?></td>
          </tr>
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
          <tr>
            <td class="main"><?php
    echo TEXT_PRODUCTS_DATE_AVAILABLE;
?></td>
            <td class="main"><?php
    echo tep_draw_input_field('products_date_available', $pInfo->products_date_available, 'id="products_date_available"') . ' <small>(YYYY-MM-DD)</small>';
?></td>
          </tr>
          <tr>
            <td colspan="2"><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
          </tr>
				<?php/*
			
<td rowspan=4 colspan=2 class="main"> 
			 //poppo http://www.cadservices.co.uk/addupgrade.php?theid=test
    echo '<strong>Upgrade options:</strong><br />';
    $select = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS . " as p, products_upgrades as u where p.products_id = u.upgrade_id");
    while ($upgradeOptions = tep_db_fetch_array($select)) {
        //echo $upgradeOptions['products_name'] . ' ' . $upgradeOptions['products_price']	. '</br>';
    }
    echo tep_draw_pull_down_menu('addupgrade', $models_array);
?>
		 <a onclick="upgradesAddToList()"> Add Product to List</a>
		 <table width="100%" id="upgradesList" class="upgradesList"><tr><th>Part Number</td><th>Description</td><th>Price</td><th>Remove</th></tr><tbody id="upgradesListBody"><tbody></table>
		  </td>

     </table></td>
      </tr>   */?>
      <tr>
        <td><?php
    echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
      <tr>
        <td class="smallText" align="right"><?php
    echo tep_draw_hidden_field('products_date_added', (@tep_not_null($pInfo->products_date_added) ? $pInfo->products_date_added : date('Y-m-d'))) . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . (isset($_GET['pID']) ? '&pID=' . $_GET['pID'] : '')));
?></td>
      </tr>
    </table>

<script type="text/javascript">
$('#products_date_available').datepicker({
  dateFormat: 'yy-mm-dd'
});
</script>
    
    </form>
<?php
} elseif ($action == 'new_product_preview') {
    $product_query = tep_db_query("select p.products_id, pd.language_id, pd.products_name, pd.products_description, pd.products_description_mini, pd.products_url, pd.products_video_url, p.products_quantity, p.products_model, p.products_image, p.products_price, p.products_weight, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.manufacturers_id  from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd where p.products_id = pd.products_id and p.products_id = '" . (int) $_GET['pID'] . "'");
    $product       = tep_db_fetch_array($product_query);
    
    $pInfo               = new objectInfo($product);
    $products_image_name = $pInfo->products_image;
    
    // start indvship
    $products_shipping_query = tep_db_query("SELECT * FROM " . TABLE_PRODUCTS_SHIPPING . " WHERE products_id=" . (int) $_GET['pID']);
    while ($products_shipping = tep_db_fetch_array($products_shipping_query)) {
        $products_ship_methods_id  = $products_shipping['products_ship_methods_id'];
        $products_ship_zip         = $products_shipping['products_ship_zip'];
        $products_ship_price       = $products_shipping['products_ship_price'];
        $products_ship_price_two   = $products_shipping['products_ship_price_two'];
        $products_ship_exempt_free = $products_shipping['products_ship_exempt_free'];
    }
    $shipping = array(
        'products_ship_methods_id' => $products_ship_methods_id,
        'products_ship_zip' => $products_ship_zip,
        'products_ship_price' => $products_ship_price,
        'products_ship_price_two' => $products_ship_price_two,
        'products_ship_exempt_free' => $products_ship_exempt_free
    );
    $pInfo->objectInfo($shipping);
    // end indvship  
    $languages = tep_get_languages();
    for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
        $pInfo->products_name        = tep_get_products_name($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_description = tep_get_products_description($pInfo->products_id, $languages[$i]['id']);
		$pInfo->products_description_mini = tep_get_products_description_mini($pInfo->products_id, $languages[$i]['id']);
        $pInfo->products_url         = tep_get_products_url($pInfo->products_id, $languages[$i]['id']);
		$pInfo->products_video_url         = tep_get_products_video_url($pInfo->products_id, $languages[$i]['id']);
?>
    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php
        echo tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . $pInfo->products_name;
?></td>
            <td class="pageHeading" align="right"><?php
        echo $currencies->format($pInfo->products_price);
?></td>
          </tr>
		  
		  
		  <?php if ($pInfo->products_video_url) {
?>
      <tr>
        <td><?php
            echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
      <tr>
        <td class="main"><div style="width:200px"><?php
			
            echo $pInfo->products_video_url;
?></div></td>
      </tr>
<?php
        }
?>
		  
		  
		  
		  
        </table></td>
      </tr>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
      <tr>
        <td class="main"><?php
        echo tep_image(DIR_WS_CATALOG_IMAGES . $products_image_name, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT, 'align="right" hspace="5" vspace="5"') . stripslashes($pInfo->products_description);
?></td>
      </tr>
<?php
        if ($pInfo->products_url) {
?>
      <tr>
        <td><?php
            echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
      <tr>
        <td class="main"><?php
            echo sprintf(TEXT_PRODUCT_MORE_INFORMATION, $pInfo->products_url);
?></td>
      </tr>
<?php
        }
?>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
<?php
        if ($pInfo->products_date_available > date('Y-m-d')) {
?>
      <tr>
        <td align="center" class="smallText"><?php
            echo sprintf(TEXT_PRODUCT_DATE_AVAILABLE, tep_date_long($pInfo->products_date_available));
?></td>
      </tr>
<?php
        } else {
?>
      <tr>
        <td align="center" class="smallText"><?php
            echo sprintf(TEXT_PRODUCT_DATE_ADDED, tep_date_long($pInfo->products_date_added));
?></td>
      </tr>
<?php
        }
?>
      <tr>
        <td><?php
        echo tep_draw_separator('pixel_trans.gif', '1', '10');
?></td>
      </tr>
<?php
    }
    
    if (isset($_GET['origin'])) {
        $pos_params = strpos($_GET['origin'], '?', 0);
        if ($pos_params != false) {
            $back_url        = substr($_GET['origin'], 0, $pos_params);
            $back_url_params = substr($_GET['origin'], $pos_params + 1);
        } else {
            $back_url        = $_GET['origin'];
            $back_url_params = '';
        }
    } else {
        $back_url        = 'categories.php';
        $back_url_params = 'cPath=' . $cPath . '&pID=' . $pInfo->products_id;
    }
?>
      <tr>
        <td align="right" class="smallText"><?php
    echo tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link($back_url, $back_url_params, 'NONSSL'));
?></td>
      </tr>
    </table>
<?php
} else {
?>
    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td class="pageHeading"><?php
    echo HEADING_TITLE;
?></td>
            <td class="pageHeading" align="right"><?php
    echo tep_draw_separator('pixel_trans.gif', 1, HEADING_IMAGE_HEIGHT);
?></td>
            <td align="right"><table border="0" width="100%" cellspacing="0" cellpadding="0">
              <tr>
                <td class="smallText" align="right">
<?php
    echo tep_draw_form('search', 'categories.php', '', 'get');
    echo HEADING_TITLE_SEARCH . ' ' . tep_draw_input_field('search');
    echo tep_hide_session_id() . '</form>';
?>
                </td>
              </tr>
              <tr>
                <td class="smallText" align="right">
<?php
    echo tep_draw_form('goto', 'categories.php', '', 'get');
    echo HEADING_TITLE_GOTO . ' ' . tep_draw_pull_down_menu('cPath', tep_get_category_tree(), $current_category_id, 'onchange="this.form.submit();"');
    echo tep_hide_session_id() . '</form>';
?>
                </td>
              </tr>
            </table></td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="0">
          <tr>
            <td valign="top"><table border="0" width="100%" cellspacing="0" cellpadding="2">
              <tr class="dataTableHeadingRow">
			  <!--BOF - Added code for Admin Sort by products model---->
<td class="dataTableHeadingContent"><?php
    echo TABLE_HEADING_PRODUCTS_MODEL;
?></td>
<!--EOF - Added code for Admin Sort by products model---->
                <td class="dataTableHeadingContent"><?php
    echo TABLE_HEADING_CATEGORIES_PRODUCTS;
?></td>
                <td class="dataTableHeadingContent" align="center"><?php
    echo TABLE_HEADING_STATUS;
?></td>
  		    <td class="dataTableHeadingContent" align="right"><?php
    echo TABLE_HEADING_ACTION;
?>&nbsp;</td>
              </tr>
<?php
    $categories_count = 0;
    $rows             = 0;
    if (isset($_GET['search'])) {
        $search = tep_db_prepare_input($_GET['search']);
        
        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, c.google_category, c.google_category_baseline from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "' and cd.categories_name like '%" . tep_db_input($search) . "%' order by c.sort_order, cd.categories_name");
    } else {
        $categories_query = tep_db_query("select c.categories_id, cd.categories_name, c.categories_image, c.parent_id, c.sort_order, c.date_added, c.last_modified, c.google_category, c.google_category_baseline from " . TABLE_CATEGORIES . " c, " . TABLE_CATEGORIES_DESCRIPTION . " cd where c.parent_id = '" . (int) $current_category_id . "' and c.categories_id = cd.categories_id and cd.language_id = '" . (int) $languages_id . "' order by c.sort_order, cd.categories_name");
    }
    while ($categories = tep_db_fetch_array($categories_query)) {
        $categories_count++;
        $rows++;
        
        // Get parent_id for subcategories if search
        if (isset($_GET['search']))
            $cPath = $categories['parent_id'];
        
        if ((!isset($_GET['cID']) && !isset($_GET['pID']) || (isset($_GET['cID']) && ($_GET['cID'] == $categories['categories_id']))) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
            $category_childs   = array(
                'childs_count' => tep_childs_in_category_count($categories['categories_id'])
            );
            $category_products = array(
                'products_count' => tep_products_in_category_count($categories['categories_id'])
            );
            
            $cInfo_array = array_merge($categories, $category_childs, $category_products);
            $cInfo       = new objectInfo($cInfo_array);
        }
        
        if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
            echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', tep_get_path($categories['categories_id'])) . '\'">' . "\n";
        } else {
            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '\'">' . "\n";
        }
?>
<!--BOF - Added code for Admin Sort by products model---->
<td class="dataTableContent" width="5%" nowrap></td>
<!--EOF - Added code for Admin Sort by products model---->
                <td class="dataTableContent"><?php
        echo '<a href="' . tep_href_link('categories.php', tep_get_path($categories['categories_id'])) . '">' . tep_image('images/icons/folder.gif', ICON_FOLDER) . '</a>&nbsp;<strong>' . $categories['categories_name'] . '</strong>';
?></td>
	         <td class="dataTableContent" align="center">&nbsp;</td>
                <td class="dataTableContent" align="right"><?php
        if (isset($cInfo) && is_object($cInfo) && ($categories['categories_id'] == $cInfo->categories_id)) {
            echo tep_image('images/icon_arrow_right.gif', '');
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $categories['categories_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>';
        }
?>&nbsp;</td>
		  </tr>

<?php
    }
    
    $products_count = 0;
    
    /*
    BOF search by model number
    */
    
    
    if (isset($_GET['search'])) {
        //      $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and pd.products_name like '%" . tep_db_input($search) . "%' order by pd.products_name");
        //    } else {
        //      $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = pd.products_id and pd.language_id = '" . (int)$languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id = '" . (int)$current_category_id . "' order by pd.products_name");
        
        
        $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.products_model, p.products_sort_order, p2c.categories_id from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' and p.products_id = p2c.products_id and (pd.products_name like '%" . tep_db_input($search) . "%'  or p.products_model like '%" . tep_db_input($search) . "%') order by pd.products_name");
    } else {
        $products_query = tep_db_query("select p.products_id, pd.products_name, p.products_quantity, p.products_image, p.products_price, p.products_date_added, p.products_last_modified, p.products_date_available, p.products_status, p.products_model, p.products_sort_order from " . TABLE_PRODUCTS . " p, " . TABLE_PRODUCTS_DESCRIPTION . " pd, " . TABLE_PRODUCTS_TO_CATEGORIES . " p2c where p.products_id = pd.products_id and pd.language_id = '" . (int) $languages_id . "' and p.products_id = p2c.products_id and p2c.categories_id = '" . (int) $current_category_id . "' order by p.products_sort_order");
    }
    
    /*
    EOF search by model number
    */
    
    while ($products = tep_db_fetch_array($products_query)) {
        $products_count++;
        $rows++;
        
        // Get categories_id for product if search
        if (isset($_GET['search']))
            $cPath = $products['categories_id'];
        
        if ((!isset($_GET['pID']) && !isset($_GET['cID']) || (isset($_GET['pID']) && ($_GET['pID'] == $products['products_id']))) && !isset($pInfo) && !isset($cInfo) && (substr($action, 0, 3) != 'new')) {
            // find out the rating average from customer reviews
            $reviews_query = tep_db_query("select (avg(reviews_rating) / 5 * 100) as average_rating from " . TABLE_REVIEWS . " where products_id = '" . (int) $products['products_id'] . "'");
            $reviews       = tep_db_fetch_array($reviews_query);
            $pInfo_array   = array_merge($products, $reviews);
            $pInfo         = new objectInfo($pInfo_array);
        }
        
        if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
            echo '              <tr id="defaultSelected" class="dataTableRowSelected" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview') . '\'">' . "\n";
        } else {
            echo '              <tr class="dataTableRow" onmouseover="rowOverEffect(this)" onmouseout="rowOutEffect(this)" onclick="document.location.href=\'' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '\'">' . "\n";
        }
?>
<!--BOF - Added code for Admin Sort by products model---->
<td class="dataTableContent" width="5%" nowrap><?php
        echo '&nbsp;' . $products['products_model'];
?></td>
<!--EOF - Added code for Admin Sort by products model---->
                <td class="dataTableContent"><?php
        echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id'] . '&action=new_product_preview') . '">' . tep_image('images/icons/preview.gif', ICON_PREVIEW) . '</a>&nbsp;' . $products['products_name'];
?></td>
                <td class="dataTableContent" align="center">
<?php
        if ($products['products_status'] == '1') {
            echo tep_image('images/icon_status_green.gif', IMAGE_ICON_STATUS_GREEN, 10, 10) . '&nbsp;&nbsp;<a href="' . tep_href_link('categories.php', 'action=setflag&flag=0&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image('images/icon_status_red_light.gif', IMAGE_ICON_STATUS_RED_LIGHT, 10, 10) . '</a>';
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'action=setflag&flag=1&pID=' . $products['products_id'] . '&cPath=' . $cPath) . '">' . tep_image('images/icon_status_green_light.gif', IMAGE_ICON_STATUS_GREEN_LIGHT, 10, 10) . '</a>&nbsp;&nbsp;' . tep_image('images/icon_status_red.gif', IMAGE_ICON_STATUS_RED, 10, 10);
        }
?></td>
	<td class="dataTableContent" align="center"><?php
        echo $products['products_sort_order'];
?></td>

                <td class="dataTableContent" align="right"><?php
        if (isset($pInfo) && is_object($pInfo) && ($products['products_id'] == $pInfo->products_id)) {
            echo tep_image('images/icon_arrow_right.gif', '');
        } else {
            echo '<a href="' . tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $products['products_id']) . '">' . tep_image('images/icon_info.gif', IMAGE_ICON_INFO) . '</a>';
        }
?>&nbsp;</td>
              </tr>
<?php
    }
    
    $cPath_back = '';
    if (sizeof($cPath_array) > 0) {
        for ($i = 0, $n = sizeof($cPath_array) - 1; $i < $n; $i++) {
            if (empty($cPath_back)) {
                $cPath_back .= $cPath_array[$i];
            } else {
                $cPath_back .= '_' . $cPath_array[$i];
            }
        }
    }
    
    $cPath_back = (@tep_not_null($cPath_back)) ? 'cPath=' . $cPath_back . '&' : '';
?>
              <tr>
                <td colspan="3"><table border="0" width="100%" cellspacing="0" cellpadding="2">
                  <tr>
                    <td class="smallText"><?php
    echo TEXT_CATEGORIES . '&nbsp;' . $categories_count . '<br />' . TEXT_PRODUCTS . '&nbsp;' . $products_count;
?></td>
                    <td align="right" class="smallText"><?php
    if (sizeof($cPath_array) > 0)
        echo tep_draw_button(IMAGE_BACK, 'triangle-1-w', tep_href_link('categories.php', $cPath_back . 'cID=' . $current_category_id));
    if (!isset($_GET['search']))
        echo tep_draw_button(IMAGE_NEW_CATEGORY, 'plus', tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_category')) . tep_draw_button(IMAGE_NEW_PRODUCT, 'plus', tep_href_link('categories.php', 'cPath=' . $cPath . '&action=new_product'));
?>&nbsp;</td>
                  </tr>
                </table></td>
              </tr>
            </table></td>
<?php
    $heading  = array();
    $contents = array();
    switch ($action) {
        case 'new_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_NEW_CATEGORY . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('newcategory', 'categories.php', 'action=insert_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"')
            );
            $contents[] = array(
                'text' => TEXT_NEW_CATEGORY_INTRO
            );
            
            $category_inputs_string = '';
            $languages              = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $category_inputs_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']');
            }
            
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_NAME . $category_inputs_string
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES_IMAGE . '<br />' . tep_draw_file_field('categories_image')
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_SORT_ORDER . '<br />' . tep_draw_input_field('sort_order', '', 'size="2"')
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath))
            );
            break;
        case 'edit_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_EDIT_CATEGORY . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=update_category&cPath=' . $cPath, 'post', 'enctype="multipart/form-data"') . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => TEXT_EDIT_INTRO
            );
            
            $category_inputs_string = '';
            $languages              = tep_get_languages();
            for ($i = 0, $n = sizeof($languages); $i < $n; $i++) {
                $category_inputs_string .= '<br />' . tep_image(tep_catalog_href_link('includes/languages/' . $languages[$i]['directory'] . '/images/' . $languages[$i]['image'], '', 'SSL'), $languages[$i]['name']) . '&nbsp;' . tep_draw_input_field('categories_name[' . $languages[$i]['id'] . ']', tep_get_category_name($cInfo->categories_id, $languages[$i]['id']));
            }
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_NAME . $category_inputs_string
            );
            $contents[]               = array(
                'text' => '<br />' . tep_image(DIR_WS_CATALOG_IMAGES . $cInfo->categories_image, $cInfo->categories_name) . '<br />' . DIR_WS_CATALOG_IMAGES . '<br /><strong>' . $cInfo->categories_image . '</strong>'
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_CATEGORIES_IMAGE . '<br />' . tep_draw_file_field('categories_image')
            );
            $contents[]               = array(
                'text' => '<br />' . TEXT_EDIT_SORT_ORDER . '<br />' . tep_draw_input_field('sort_order', $cInfo->sort_order, 'size="2"')
            );
            $contents[]               = array(
                'text' => '<br />Google feed category (see <a href="http://support.google.com/merchants/bin/answer.py?hl=en&answer=1705911" target="_blank">here</a>)<br />Note:The first category should have the default categories up to the baseline and all categories below should be just one category' . tep_draw_input_field('google_category', $cInfo->google_category, 'size="50"')
            );
            $google_category_baseline = '';
            if ($cInfo->google_category_baseline == 1) {
                $google_category_baseline = 'checked';
            };
            $contents[] = array(
                'text' => '<br />New Baseline<br />Note: Only tick this box if you need to override a parent category baseline (root categories ignore this field as it is required as that case. ' . $cInfo->google_category_baseline . ' ' . tep_draw_checkbox_field('google_category_baseline', 1, $google_category_baseline)
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_SAVE, 'disk', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'delete_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_DELETE_CATEGORY . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=delete_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => TEXT_DELETE_CATEGORY_INTRO
            );
            $contents[] = array(
                'text' => '<br /><strong>' . $cInfo->categories_name . '</strong>'
            );
            if ($cInfo->childs_count > 0)
                $contents[] = array(
                    'text' => '<br />' . sprintf(TEXT_DELETE_WARNING_CHILDS, $cInfo->childs_count)
                );
            if ($cInfo->products_count > 0)
                $contents[] = array(
                    'text' => '<br />' . sprintf(TEXT_DELETE_WARNING_PRODUCTS, $cInfo->products_count)
                );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'move_category':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_MOVE_CATEGORY . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('categories', 'categories.php', 'action=move_category_confirm&cPath=' . $cPath) . tep_draw_hidden_field('categories_id', $cInfo->categories_id)
            );
            $contents[] = array(
                'text' => sprintf(TEXT_MOVE_CATEGORIES_INTRO, $cInfo->categories_name)
            );
            $contents[] = array(
                'text' => '<br />' . sprintf(TEXT_MOVE, $cInfo->categories_name) . '<br />' . tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id)
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'arrow-4', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&cID=' . $cInfo->categories_id))
            );
            break;
        case 'delete_product':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_DELETE_PRODUCT . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('products', 'categories.php', 'action=delete_product_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => TEXT_DELETE_PRODUCT_INTRO
            );
            $contents[] = array(
                'text' => '<br /><strong>' . $pInfo->products_name . '</strong>'
            );
            
            $product_categories_string = '';
            $product_categories        = tep_generate_category_path($pInfo->products_id, 'product');
            for ($i = 0, $n = sizeof($product_categories); $i < $n; $i++) {
                $category_path = '';
                for ($j = 0, $k = sizeof($product_categories[$i]); $j < $k; $j++) {
                    $category_path .= $product_categories[$i][$j]['text'] . '&nbsp;&gt;&nbsp;';
                }
                $category_path = substr($category_path, 0, -16);
                $product_categories_string .= tep_draw_checkbox_field('product_categories[]', $product_categories[$i][sizeof($product_categories[$i]) - 1]['id'], true) . '&nbsp;' . $category_path . '<br />';
            }
            $product_categories_string = substr($product_categories_string, 0, -4);
            
            $contents[] = array(
                'text' => '<br />' . $product_categories_string
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_DELETE, 'trash', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        case 'move_product':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_MOVE_PRODUCT . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('products', 'categories.php', 'action=move_product_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => sprintf(TEXT_MOVE_PRODUCTS_INTRO, $pInfo->products_name)
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><strong>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</strong>'
            );
            $contents[] = array(
                'text' => '<br />' . sprintf(TEXT_MOVE, $pInfo->products_name) . '<br />' . tep_draw_pull_down_menu('move_to_category_id', tep_get_category_tree(), $current_category_id)
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_MOVE, 'arrow-4', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        case 'copy_to':
            $heading[] = array(
                'text' => '<strong>' . TEXT_INFO_HEADING_COPY_TO . '</strong>'
            );
            
            $contents   = array(
                'form' => tep_draw_form('copy_to', 'categories.php', 'action=copy_to_confirm&cPath=' . $cPath) . tep_draw_hidden_field('products_id', $pInfo->products_id)
            );
            $contents[] = array(
                'text' => TEXT_INFO_COPY_TO_INTRO
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_INFO_CURRENT_CATEGORIES . '<br /><strong>' . tep_output_generated_category_path($pInfo->products_id, 'product') . '</strong>'
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_CATEGORIES . '<br />' . tep_draw_pull_down_menu('categories_id', tep_get_category_tree(), $current_category_id)
            );
            $contents[] = array(
                'text' => '<br />' . TEXT_HOW_TO_COPY . '<br />' . tep_draw_radio_field('copy_as', 'link', true) . ' ' . TEXT_COPY_AS_LINK . '<br />' . tep_draw_radio_field('copy_as', 'duplicate') . ' ' . TEXT_COPY_AS_DUPLICATE
            );
            $contents[] = array(
                'align' => 'center',
                'text' => '<br />' . tep_draw_button(IMAGE_COPY, 'copy', null, 'primary') . tep_draw_button(IMAGE_CANCEL, 'close', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id))
            );
            break;
        default:
            if ($rows > 0) {
                if (isset($cInfo) && is_object($cInfo)) { // category info box contents
                    $category_path_string = '';
                    $category_path        = tep_generate_category_path($cInfo->categories_id);
                    for ($i = (sizeof($category_path[0]) - 1); $i > 0; $i--) {
                        $category_path_string .= $category_path[0][$i]['id'] . '_';
                    }
                    $category_path_string = substr($category_path_string, 0, -1);
                    
                    $heading[] = array(
                        'text' => '<strong>' . $cInfo->categories_name . '</strong>'
                    );
                    
                    $contents[] = array(
                        'align' => 'center',
                        'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=edit_category')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=delete_category')) . tep_draw_button(IMAGE_MOVE, 'arrow-4', tep_href_link('categories.php', 'cPath=' . $category_path_string . '&cID=' . $cInfo->categories_id . '&action=move_category'))
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($cInfo->date_added)
                    );
                    if (@tep_not_null($cInfo->last_modified))
                        $contents[] = array(
                            'text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($cInfo->last_modified)
                        );
                    $contents[] = array(
                        'text' => '<br />' . tep_info_image($cInfo->categories_image, $cInfo->categories_name, HEADING_IMAGE_WIDTH, HEADING_IMAGE_HEIGHT) . '<br />' . $cInfo->categories_image
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_SUBCATEGORIES . ' ' . $cInfo->childs_count . '<br />' . TEXT_PRODUCTS . ' ' . $cInfo->products_count
                    );
                } elseif (isset($pInfo) && is_object($pInfo)) { // product info box contents
                    $heading[] = array(
                        'text' => '<strong>' . tep_get_products_name($pInfo->products_id, $languages_id) . '</strong>'
                    );
                    
                    $contents[] = array(
                        'align' => 'center',
                        'text' => tep_draw_button(IMAGE_EDIT, 'document', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=new_product')) . tep_draw_button(IMAGE_DELETE, 'trash', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=delete_product')) . tep_draw_button(IMAGE_MOVE, 'arrow-4', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=move_product')) . tep_draw_button(IMAGE_COPY_TO, 'copy', tep_href_link('categories.php', 'cPath=' . $cPath . '&pID=' . $pInfo->products_id . '&action=copy_to'))
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_DATE_ADDED . ' ' . tep_date_short($pInfo->products_date_added)
                    );
                    if (@tep_not_null($pInfo->products_last_modified))
                        $contents[] = array(
                            'text' => TEXT_LAST_MODIFIED . ' ' . tep_date_short($pInfo->products_last_modified)
                        );
                    if (date('Y-m-d') < $pInfo->products_date_available)
                        $contents[] = array(
                            'text' => TEXT_DATE_AVAILABLE . ' ' . tep_date_short($pInfo->products_date_available)
                        );
                    $contents[] = array(
                        'text' => '<br />' . tep_info_image($pInfo->products_image, $pInfo->products_name, SMALL_IMAGE_WIDTH, SMALL_IMAGE_HEIGHT) . '<br />' . $pInfo->products_image
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_PRODUCTS_PRICE_INFO . ' ' . $currencies->format($pInfo->products_price) . '<br />' . TEXT_PRODUCTS_QUANTITY_INFO . ' ' . $pInfo->products_quantity
                    );
                    $contents[] = array(
                        'text' => '<br />' . TEXT_PRODUCTS_AVERAGE_RATING . ' ' . number_format($pInfo->average_rating, 2) . '%'
                    );
                }
            } else { // create category/product info
                $heading[] = array(
                    'text' => '<strong>' . EMPTY_CATEGORY . '</strong>'
                );
                
                $contents[] = array(
                    'text' => TEXT_NO_CHILD_CATEGORIES_OR_PRODUCTS
                );
            }
            break;
    }
    
    if ((@tep_not_null($heading)) && (@tep_not_null($contents))) {
        echo '            <td width="25%" valign="top">' . "\n";
        
        $box = new box;
        echo $box->infoBox($heading, $contents);
        
        echo '            </td>' . "\n";
    }
?>
          </tr>
        </table></td>
      </tr>
    </table>
<?php
}

require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>