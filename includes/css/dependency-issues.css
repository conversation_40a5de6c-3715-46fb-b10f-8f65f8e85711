/**
 * CSS styles for dependency issue display and info modals
 */

/* Info button styling */
.dependency-info-btn {
    margin-left: 8px;
    padding: 2px 6px;
    font-size: 11px;
    line-height: 1.2;
    border-radius: 3px;
    vertical-align: middle;
    cursor: pointer;
    transition: all 0.2s ease;
}

.dependency-info-btn:hover {
    background-color: #31708f;
    border-color: #245269;
    transform: scale(1.05);
}

.dependency-info-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(49, 112, 143, 0.3);
}

/* Modal overlay improvements */
.dependency-modal-overlay {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* Modal content styling */
.dependency-modal-content {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid #e5e7eb;
}

/* Modal header styling */
.dependency-modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.dependency-modal-title {
    color: #2c3e50;
    font-weight: 600;
}

.dependency-modal-title i {
    color: #3498db;
}

/* Modal body styling */
.dependency-modal-body h4 {
    color: #34495e;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.dependency-modal-body p,
.dependency-modal-body div {
    line-height: 1.6;
    color: #555;
}

/* Resolution steps styling */
.dependency-modal-body .mb-2 {
    margin-bottom: 8px;
    padding-left: 0;
}

.dependency-modal-body .mb-4 {
    margin-bottom: 15px;
}

/* Button styling in modal */
.dependency-modal-footer .btn {
    transition: all 0.2s ease;
}

.dependency-modal-footer .btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Fixed positioning utilities */
.fixed {
    position: fixed;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.bg-black {
    background-color: rgba(0, 0, 0, 0.5);
}

.bg-white {
    background-color: white;
}

.rounded-lg {
    border-radius: 8px;
}

.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.max-w-2xl {
    max-width: 600px;
}

.w-full {
    width: 100%;
}

.mx-4 {
    margin-left: 20px;
    margin-right: 20px;
}

/* Issue type indicators */
.issue-type-missing_dependency::before {
    content: "🔗 ";
    margin-right: 4px;
}

.issue-type-circular_dependency::before {
    content: "🔄 ";
    margin-right: 4px;
}

.issue-type-sort_order_violation::before {
    content: "📊 ";
    margin-right: 4px;
}

.issue-type-missing_variation::before {
    content: "🎯 ";
    margin-right: 4px;
}

.issue-type-default_conflict::before {
    content: "⚠️ ";
    margin-right: 4px;
}

.issue-type-self_option_group_dependency::before {
    content: "🚫 ";
    margin-right: 4px;
}

.issue-type-variation_missing_attribute::before {
    content: "❌ ";
    margin-right: 4px;
}

.issue-type-variation_duplicate_model::before {
    content: "📋 ";
    margin-right: 4px;
}

.issue-type-variation_missing_autodesk_link::before {
    content: "🔗 ";
    margin-right: 4px;
}

.issue-type-variation_invalid_autodesk_link::before {
    content: "❌ ";
    margin-right: 4px;
}

.issue-type-variation_selection_issue::before {
    content: "⚡ ";
    margin-right: 4px;
}

/* Responsive modal */
@media (max-width: 768px) {
    .dependency-modal-content {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }
    
    .dependency-modal-header,
    .dependency-modal-body,
    .dependency-modal-footer {
        padding: 12px;
    }
    
    .dependency-modal-title {
        font-size: 16px;
    }
}

/* Animation improvements */
.dependency-modal-enter {
    animation: dependencyModalFadeIn 0.3s ease-out;
}

.dependency-modal-leave {
    animation: dependencyModalFadeOut 0.2s ease-in;
}

@keyframes dependencyModalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes dependencyModalFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

/* Z-index management */
.z-50 {
    z-index: 1050;
}

/* Ensure proper stacking */
.dependency-modal-overlay {
    z-index: 1050;
}

.dependency-modal-content {
    position: relative;
    z-index: 1051;
}
