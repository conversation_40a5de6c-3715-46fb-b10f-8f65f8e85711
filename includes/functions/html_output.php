<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

////
// The HTML href link wrapper function
 /* function tep_href_link($page = '', $parameters = '', $connection = 'NONSSL', $add_session_id = true, $search_engine_safe = true) {
    global $request_type, $session_started, $SID;

    $page = tep_output_string($page);

    if (!@tep_not_null($page)) {
      die('</td></tr></table></td></tr></table><br /><br /><font color="#ff0000"><strong>Error!</strong></font><br /><br /><strong>Unable to determine the page link!<br /><br />');
    }

    if ($connection == 'NONSSL') {
      $link = HTTP_SERVER . DIR_WS_HTTP_CATALOG;
    } elseif ($connection == 'SSL') {
      if (ENABLE_SSL == true) {
        $link = HTTPS_SERVER . DIR_WS_HTTPS_CATALOG;
      } else {
        $link = HTTP_SERVER . DIR_WS_HTTP_CATALOG;
      }
    } else {
      die('</td></tr></table></td></tr></table><br /><br /><font color="#ff0000"><strong>Error!</strong></font><br /><br /><strong>Unable to determine connection method on a link!<br /><br />Known methods: NONSSL SSL</strong><br /><br />');
    }

    if (@tep_not_null($parameters)) {
      $link .= $page . '?' . tep_output_string($parameters);
      $separator = '&';
    } else {
      $link .= $page;
      $separator = '?';
    }

    while ( (substr($link, -1) == '&') || (substr($link, -1) == '?') ) $link = substr($link, 0, -1);

// Add the session ID when moving from different HTTP and HTTPS servers, or when SID is defined
    if ( ($add_session_id == true) && ($session_started == true) && (SESSION_FORCE_COOKIE_USE == 'False') ) {
      if (isset($SID) && @tep_not_null($SID)) {
        $_sid = $SID;
      } elseif ( ( ($request_type == 'NONSSL') && ($connection == 'SSL') && (ENABLE_SSL == true) ) || ( ($request_type == 'SSL') && ($connection == 'NONSSL') ) ) {
        if (HTTP_COOKIE_DOMAIN != HTTPS_COOKIE_DOMAIN) {
          $_sid = tep_session_name() . '=' . tep_session_id();
        }
      }
    }

    if (isset($_sid)) {
      $link .= $separator . tep_output_string($_sid);
    }

    while (strpos($link, '&&') !== false) $link = str_replace('&&', '&', $link);

    if ( (SEARCH_ENGINE_FRIENDLY_URLS == 'true') && ($search_engine_safe == true) ) {
      $link = str_replace('?', '/', $link);
      $link = str_replace('&', '/', $link);
      $link = str_replace('=', '/', $link);
    } else {
      $link = str_replace('&', '&amp;', $link);
    }

    return $link;
  }*/
  
  ////
// Ultimate SEO URLs v2.2d
// The HTML href link wrapper function
 function tep_href_link($page = '', $parameters = '', $connection = 'NONSSL', $add_session_id = true, $search_engine_safe = true) {
   global $seo_urls;                
   if ( !is_object($seo_urls) ){
    if ( !class_exists('SEO_URL') ){
     include_once('includes/classes/seo.class.php');
    }
    global $languages_id;
    $seo_urls = new SEO_URL($languages_id);
   }
   return $seo_urls->href_link($page, $parameters, $connection, $add_session_id);
 }
////
// New HTML image wrapper function modified for KISS Image Thumbnailer by FWR Media
  function tep_image($src, $alt = '', $width = '', $height = '', $parameters = '', $responsive = true, $bootstrap_css = '') {
    // Include the Database installation file if executed for the first time or update required.
    if ( !defined('KISSIT_USE_SUBSET') ) require_once 'includes/modules/kiss_image_thumbnailer/db_install.php';
    // If width and height are not numeric then we use the original image size to be able to generate smaller images for srcset
    // If original image width and height are less than KISSIT_MIN_IMAGE_SIZE, we do not need a thumb
    if ( is_file($src ) ) {
      $original_img_size = getimagesize($src);
      if ( !is_numeric($width) || !is_numeric($height) ) {
        $width = $original_img_size[0];
        $height = $original_img_size[1];
        if ( defined('KISSIT_BANNER_WATERMARK') && KISSIT_BANNER_WATERMARK != 'true' ) $bootstrap_css .= 'no-watermark';
      }
      if ( !is_numeric ( $width ) || !is_numeric ( $height ) || $original_img_size[0] < KISSIT_MIN_IMAGE_SIZE || $original_img_size[1] < KISSIT_MIN_IMAGE_SIZE ) return tep_image_legacy( $src, $alt, $width, $height, $parameters, $responsive, $bootstrap_css );
    } else {
      if ( !is_numeric ( $width ) || !is_numeric ( $height ) ) return tep_image_legacy( $src, $alt, $width, $height, $parameters, $responsive, $bootstrap_css );
    }
    // Create thumbs main dir and .htaccess.	
		if(!is_dir('images/' . KISSIT_THUMBS_MAIN_DIR))mkdir('images/' . KISSIT_THUMBS_MAIN_DIR, 0777);
	  if ( !is_file('images/' . KISSIT_THUMBS_MAIN_DIR . '.htaccess') ) {
	    $hpname = 'images/' . KISSIT_THUMBS_MAIN_DIR . '.htaccess';
			//define .htaccess content
	     $htacces = '
<FilesMatch "\.(php([0-9]|s)?|s?p?html|cgi|pl|exe)$">
   Order Deny,Allow
   Deny from all
</FilesMatch>';

	    if ($hp = fopen($hpname,'w')) {
		  fwrite($hp,$htacces);
		  fclose($hp);
	    } 
	  }
    // Create thumbs sub dirs and .htaccess.	
    $thumbs_dir_path = str_replace('images/', 'images/' . KISSIT_THUMBS_MAIN_DIR . $width .'_'.$height.'/', dirname($src) . '/');
    $thumbs_dir = '';
    $thumbs_dir_paths = explode("/",$thumbs_dir_path);
    for ($i=0, $n=sizeof($thumbs_dir_paths); $i<$n; $i++) {
    	$thumbs_dir .= $thumbs_dir_paths[$i] . '/';
    	if(!is_dir($thumbs_dir))mkdir($thumbs_dir, 0777);
    	// create .htacces protection like in main image dir
    	if (($i==$n-1) && (!is_file($thumbs_dir . '.htaccess')) ) {
    		$hpname = $thumbs_dir . '.htaccess';
    		//define .htaccess content
    		$htacces = '
<FilesMatch "\.(php([0-9]|s)?|s?p?html|cgi|pl|exe)$">
   Order Deny,Allow
   Deny from all
</FilesMatch>';
	    	if ($hp = fopen($hpname,'w')) {
	    		fwrite($hp,$htacces);
	    		fclose($hp);
	    	} 
	    }
	   } // end for
	    // End create subdirectory and .htaccess.	
  
    require_once 'includes/modules/kiss_image_thumbnailer/classes/Image_Helper.php';
    $attributes = array( 'alt' => $alt, 'width' => $width, 'height' => $height );
    
    $image = null;
    if (@tep_not_null($width) && @tep_not_null($height)) {
      $image .= ' width="' . tep_output_string($width) . '" height="' . tep_output_string($height) . '"';
    }

    $bs_parameters = ' class="';

    if (@tep_not_null($responsive) && ($responsive === true)) {
      $bs_parameters .= 'img-responsive';
    }

    if (@tep_not_null($bootstrap_css)) $bs_parameters .= ' ' . $bootstrap_css;

    $bs_parameters .= '"';

    if (@tep_not_null($parameters)) $bs_parameters .= ' ' . $parameters;
    
    $image = new Image_Helper( array( 'src'                   => $src,
                                      'attributes'            => $attributes,
                                      'parameters'            => $bs_parameters,
                                      'default_missing_image' => 'images/' . KISSIT_DEFAULT_MISSING_IMAGE,
                                      'isXhtml'               => true,
                                      'thumbs_dir_path'       => $thumbs_dir_path,
                                      'thumb_quality'         => KISSIT_JPEG_QUALITY,
                                      'thumb_background_rgb'  => array_combine(array('red', 'green', 'blue'), explode (',', defined('KISSIT_BACKGROUND_COLOR')? KISSIT_BACKGROUND_COLOR: ('255,255,255')))
                                     )
      												);
    
    if ( false === $image_assembled = $image->assemble() ) {
      return tep_image_legacy( $src, $alt, $width, $height, $parameters, $responsive, $bootstrap_css );
    }

    
    return $image_assembled;
  } // end function

////
// The HTML image wrapper function
  function tep_image_legacy($src, $alt = '', $width = '', $height = '', $parameters = '', $responsive = true, $bootstrap_css = '') {


    if ( (empty($src) || ($src == 'images/')) && (IMAGE_REQUIRED == 'false') ) {
      return false;
    }

// alt is added to the img tag even if it is null to prevent browsers from outputting
// the image filename as default
	   /* $image = '<img src="' . tep_output_string($src) . '" alt="' . tep_output_string($alt) . '"';*/

	if (file_exists($src)) {
		$image = '<img src="' . tep_output_string($src) . '?hash=' . filemtime($src) . '" alt="' . tep_output_string($alt) . '"';
	} else {
		$image = '<img src="' . tep_output_string($src) . '?hash=0" alt="' . tep_output_string($alt) . '"';
	}

    if (@tep_not_null($alt)) {
      $image .= ' title="' . tep_output_string($alt) . '"';
    }

    if ( (CONFIG_CALCULATE_IMAGE_SIZE == 'true') && (empty($width) || empty($height)) ) {
      if ($image_size = @getimagesize($src)) {
        if (empty($width) && @tep_not_null($height)) {
          $ratio = $height / $image_size[1];
          $width = (int)($image_size[0] * $ratio);
        } elseif (@tep_not_null($width) && empty($height)) {
          $ratio = $width / $image_size[0];
          $height = (int)($image_size[1] * $ratio);
        } elseif (empty($width) && empty($height)) {
          $width = $image_size[0];
          $height = $image_size[1];
        }
      } elseif (IMAGE_REQUIRED == 'false') {
        return false;
      }
    }

    if (@tep_not_null($width) && @tep_not_null($height)) {
      $image .= ' width="' . tep_output_string($width) . '" height="' . tep_output_string($height) . '"';
    }

    $image .= ' class="';

    if (@tep_not_null($responsive) && ($responsive === true)) {
      $image .= 'img-responsive';
    }

    if (@tep_not_null($bootstrap_css)) $image .= ' ' . $bootstrap_css;

    $image .= '"';

    if (@tep_not_null($parameters)) $image .= ' ' . $parameters;

    $image .= ' />';

    return $image;
  }

////
// The HTML form submit button wrapper function
// Outputs a button in the selected language
  function tep_image_submit($image, $alt = '', $parameters = '') {
    global $language;

    $image_submit = '<input type="image" src="' . tep_output_string('includes/languages/' . $language . '/images/buttons/' . $image) . '" alt="' . tep_output_string($alt) . '"';

    if (@tep_not_null($alt)) $image_submit .= ' title=" ' . tep_output_string($alt) . ' "';

    if (@tep_not_null($parameters)) $image_submit .= ' ' . $parameters;

    $image_submit .= ' />';

    return $image_submit;
  }

////
// Output a function button in the selected language
  function tep_image_button($image, $alt = '', $parameters = '') {
    global $language;

    return tep_image('includes/languages/' . $language . '/images/buttons/' . $image, $alt, '', '', $parameters);
  }

////
// Output a separator either through whitespace, or with an image
  function tep_draw_separator($image = 'pixel_black.gif', $width = '100%', $height = '1') {
    return tep_image('images/' . $image, '', $width, $height);
  }

////
// Output a form with enhanced CSRF protection
  function tep_draw_form($name, $action, $method = 'post', $parameters = '', $tokenize = false, $csrf_action = 'default') {
    global $sessiontoken;

    $form = '<form name="' . tep_output_string($name) . '" action="' . tep_output_string($action) . '" method="' . tep_output_string($method) . '"';

    if (@tep_not_null($parameters)) $form .= ' ' . $parameters;

    $form .= '>';

    // Add CSRF protection if enabled and tokenize is true
    if ($tokenize == true) {
      if (tep_csrf_protection_enabled()) {
        // Add new CSRF token
        $form .= tep_csrf_token_field($csrf_action);
      }

      // Keep legacy sessiontoken for backward compatibility
      if (isset($sessiontoken)) {
        $form .= '<input type="hidden" name="formid" value="' . tep_output_string($sessiontoken) . '" />';
      }
    }

    return $form;
  }

////
// Output a form input field
  function tep_draw_input_field($name, $value = '', $parameters = '', $type = 'text', $reinsert_value = true, $class = 'class="form-control"') {
    $field = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';

    if ( ($reinsert_value == true) && ( (isset($_GET[$name]) && is_string($_GET[$name])) || (isset($_POST[$name]) && is_string($_POST[$name])) ) ) {
      if (isset($_GET[$name]) && is_string($_GET[$name])) {
        $value = stripslashes($_GET[$name]);
      } elseif (isset($_POST[$name]) && is_string($_POST[$name])) {
        $value = stripslashes($_POST[$name]);
      }
    }

    if (@tep_not_null($value)) {
      $field .= ' value="' . tep_output_string($value) . '"';
    }

    if (@tep_not_null($parameters)) $field .= ' ' . $parameters;

    if (@tep_not_null($class)) $field .= ' ' . $class;

    $field .= ' />';

    return $field;
  }
/*
////
// Output a form password field
// DEPRECATED AS OF 12 June 2015
  function tep_draw_password_field($name, $value = '', $parameters = '') {
    return tep_draw_input_field($name, $value, $parameters, 'password', false);
  }
*/

////
// Output a selection field - alias function for tep_draw_checkbox_field() and tep_draw_radio_field()
  function tep_draw_selection_field($name, $type, $value = '', $checked = false, $parameters = '') {
    $selection = '<input type="' . tep_output_string($type) . '" name="' . tep_output_string($name) . '"';

    if (@tep_not_null($value)) $selection .= ' value="' . tep_output_string($value) . '"';

    if ( ($checked == true) || (isset($_GET[$name]) && is_string($_GET[$name]) && (($_GET[$name] == 'on') || (stripslashes($_GET[$name]) == $value))) || (isset($_POST[$name]) && is_string($_POST[$name]) && (($_POST[$name] == 'on') || (stripslashes($_POST[$name]) == $value))) ) {
      $selection .= ' checked="checked"';
    }

    if (@tep_not_null($parameters)) $selection .= ' ' . $parameters;

    $selection .= ' />';

    return $selection;
  }

////
// Output a form checkbox field
  function tep_draw_checkbox_field($name, $value = '', $checked = false, $parameters = '') {
    return tep_draw_selection_field($name, 'checkbox', $value, $checked, $parameters);
  }

////
// Output a form radio field
  function tep_draw_radio_field($name, $value = '', $checked = false, $parameters = '') {
    return tep_draw_selection_field($name, 'radio', $value, $checked, $parameters);
  }

////
// Output a form textarea field
// The $wrap parameter is no longer used in the core xhtml template
  function tep_draw_textarea_field($name, $wrap, $width, $height, $text = '', $parameters = '', $reinsert_value = true) {
    $field = '<textarea class="form-control" name="' . tep_output_string($name) . '" cols="' . tep_output_string($width) . '" rows="' . tep_output_string($height) . '"';

    if (@tep_not_null($parameters)) $field .= ' ' . $parameters;

    $field .= '>';

    if ( ($reinsert_value == true) && ( (isset($_GET[$name]) && is_string($_GET[$name])) || (isset($_POST[$name]) && is_string($_POST[$name])) ) ) {
      if (isset($_GET[$name]) && is_string($_GET[$name])) {
        $field .= tep_output_string_protected(stripslashes($_GET[$name]));
      } elseif (isset($_POST[$name]) && is_string($_POST[$name])) {
        $field .= tep_output_string_protected(stripslashes($_POST[$name]));
      }
    } elseif (@tep_not_null($text)) {
      $field .= tep_output_string_protected($text);
    }

    $field .= '</textarea>';

    return $field;
  }

////
// Output a form hidden field
  function tep_draw_hidden_field($name, $value = '', $parameters = '') {
    $field = '<input type="hidden" name="' . tep_output_string($name) . '"';

    if (@tep_not_null($value)) {
      $field .= ' value="' . tep_output_string($value) . '"';
    } elseif ( (isset($_GET[$name]) && is_string($_GET[$name])) || (isset($_POST[$name]) && is_string($_POST[$name])) ) {
      if ( (isset($_GET[$name]) && is_string($_GET[$name])) ) {
        $field .= ' value="' . tep_output_string(stripslashes($_GET[$name])) . '"';
      } elseif ( (isset($_POST[$name]) && is_string($_POST[$name])) ) {
        $field .= ' value="' . tep_output_string(stripslashes($_POST[$name])) . '"';
      }
    }

    if (@tep_not_null($parameters)) $field .= ' ' . $parameters;

    $field .= ' />';

    return $field;
  }

////
// Hide form elements
  function tep_hide_session_id() {
    global $session_started, $SID;

    if (($session_started == true) && @tep_not_null($SID)) {
      return tep_draw_hidden_field(tep_session_name(), tep_session_id());
    }
  }

////
// Output a form pull down menu
  function tep_draw_pull_down_menu($name, $values, $default = '', $parameters = '', $required = false) {
    $field = '<select name="' . tep_output_string($name) . '"';

    if (@tep_not_null($parameters)) $field .= ' ' . $parameters;

    $field .= ' class="form-control">';

    if (empty($default) && ( (isset($_GET[$name]) && is_string($_GET[$name])) || (isset($_POST[$name]) && is_string($_POST[$name])) ) ) {
      if (isset($_GET[$name]) && is_string($_GET[$name])) {
        $default = stripslashes($_GET[$name]);
      } elseif (isset($_POST[$name]) && is_string($_POST[$name])) {
        $default = stripslashes($_POST[$name]);
      }
    }

    for ($i=0, $n=sizeof($values); $i<$n; $i++) {
      $field .= '<option value="' . tep_output_string($values[$i]['id']) . '"';
      if ($default == $values[$i]['id']) {
        $field .= ' selected="selected"';
      }

      $field .= '>' . tep_output_string($values[$i]['text'], array('"' => '&quot;', '\'' => '&#039;', '<' => '&lt;', '>' => '&gt;')) . '</option>';
    }
    $field .= '</select>';

    if ($required == true) $field .= TEXT_FIELD_REQUIRED;

    return $field;
  }

////
// Creates a pull-down list of countries
  function tep_get_country_list($name, $selected = '', $parameters = '') {
    $countries_array = array(array('id' => '', 'text' => PULL_DOWN_DEFAULT));
    $countries = tep_get_countries();

    for ($i=0, $n=sizeof($countries); $i<$n; $i++) {
      $countries_array[] = array('id' => $countries[$i]['countries_id'], 'text' => $countries[$i]['countries_name']);
    }

    return tep_draw_pull_down_menu($name, $countries_array, $selected, $parameters);
  }

////
// Output a jQuery UI Button
  function tep_draw_button($title = null, $icon = null, $link = null, $priority = null, $params = null, $style = null) {
    static $button_counter = 1;

    $types = array('submit', 'button', 'reset');

    if ( !isset($params['type']) ) {
      $params['type'] = 'submit';
    }

    if ( !in_array($params['type'], $types) ) {
      $params['type'] = 'submit';
    }

    if ( ($params['type'] == 'submit') && isset($link) ) {
      $params['type'] = 'button';
    }

    if (!isset($priority)) {
      $priority = 'secondary';
    }

    $button = NULL;

    if ( ($params['type'] == 'button') && isset($link) ) {
      $button .= '<a id="btn' . $button_counter . '" href="' . $link . '"';

      if ( isset($params['newwindow']) ) {
        $button .= ' target="_blank"';
      }
    } else {
      $button .= '<button ';
      $button .= ' type="' . tep_output_string($params['type']) . '"';
    }

    if ( isset($params['params']) ) {
      $button .= ' ' . $params['params'];
    }

    $button .= ' class="btn ';

    $button .= (isset($style)) ? $style : 'btn-default';
	
	if ( trim($title) == 'Buy' || trim($title) == 'Buy Now' || trim($title) == 'Add to Cart' || trim($title) == 'Add')  {
	 $button .= '" rel="nofollow';
	}

    $button .= '">';

    if (isset($icon) && @tep_not_null($icon)) {
      $button .= ' <span class="' . $icon . '"></span> ';
    }

    $button .= $title;

    if ( ($params['type'] == 'button') && isset($link) ) {
      $button .= '</a>';
    } else {
      $button .= '</button>';
    }

    $button_counter++;

    return $button;
  }

  // review stars
  function tep_draw_stars($rating = 0) {
    $stars = str_repeat('<span class="fas fa-star"></span>', (int)$rating);
    $stars .= str_repeat('<span class="far fa-star"></span>', 5-(int)$rating);

    return $stars;
  }

////
// Enhanced CSRF helper functions

////
// Output CSRF token hidden field
  function tep_draw_csrf_token_field($action = 'default', $field_name = 'csrf_token') {
    if (tep_csrf_protection_enabled()) {
      return tep_csrf_token_field($action, $field_name);
    }
    return '';
  }

////
// Output CSRF meta tag for JavaScript
  function tep_draw_csrf_meta_tag($action = 'default') {
    if (tep_csrf_protection_enabled()) {
      return tep_csrf_meta_tag($action);
    }
    return '';
  }

////
// Enhanced form with automatic CSRF protection
  function tep_draw_secure_form($name, $action, $method = 'post', $parameters = '', $csrf_action = 'default') {
    return tep_draw_form($name, $action, $method, $parameters, true, $csrf_action);
  }
  