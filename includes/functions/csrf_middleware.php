<?php
/*
  CSRF Validation Middleware
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2024 osCommerce

  Released under the GNU General Public License
*/

/**
 * CSRF validation middleware for form processing
 * Include this at the top of form processing scripts
 */

/**
 * Validate CSRF token for current request
 * 
 * @param string $action Optional action name for validation
 * @param bool $die_on_failure Whether to die on validation failure (default: true)
 * @param string $redirect_url URL to redirect to on failure (if not dying)
 * @return bool True if validation passes, false otherwise
 */
function tep_csrf_validate_or_die($action = 'default', $die_on_failure = true, $redirect_url = '') {
    global $messageStack;
    
    // Skip validation if CSRF protection is disabled
    if (!tep_csrf_protection_enabled()) {
        return true;
    }
    
    // Only validate POST requests by default
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return true;
    }
    
    // Check for CSRF token in POST data
    $valid = false;
    
    // Try new CSRF token first
    if (isset($_POST['csrf_token'])) {
        $valid = tep_csrf_validate_request($_POST, $action);
    }
    
    // Fallback to legacy sessiontoken validation for backward compatibility
    if (!$valid) {
        $valid = tep_validate_form_token($_POST);
    }
    
    if (!$valid) {
        // Log the CSRF attempt
        error_log('CSRF validation failed for action: ' . $action . ' from IP: ' . tep_get_ip_address());
        
        if ($die_on_failure) {
            // Add error message if messageStack is available
            if (isset($messageStack) && is_object($messageStack)) {
                $messageStack->add_session('general', 'Security validation failed. Please try again.', 'error');
            }
            
            // Redirect to safe page or show error
            if (!empty($redirect_url)) {
                tep_redirect($redirect_url);
            } else {
                tep_redirect(tep_href_link('index.php'));
            }
        }
        
        return false;
    }
    
    return true;
}

/**
 * Quick CSRF validation for AJAX requests
 * Returns JSON response on failure
 * 
 * @param string $action Optional action name
 * @return bool True if valid, exits with JSON on failure
 */
function tep_csrf_validate_ajax($action = 'default') {
    if (!tep_csrf_protection_enabled()) {
        return true;
    }
    
    $valid = false;
    
    // Check POST data
    if (!empty($_POST)) {
        $valid = tep_csrf_validate_request($_POST, $action) || tep_validate_form_token($_POST);
    }
    
    // Check GET data for AJAX requests that use GET
    if (!$valid && !empty($_GET)) {
        $valid = tep_csrf_validate_request($_GET, $action);
    }
    
    if (!$valid) {
        header('Content-Type: application/json');
        http_response_code(403);
        echo json_encode(array(
            'error' => true,
            'message' => 'Security validation failed',
            'code' => 'CSRF_VALIDATION_FAILED'
        ));
        exit;
    }
    
    return true;
}

/**
 * Validate CSRF for API endpoints
 * 
 * @param array $input_params Combined GET/POST parameters
 * @param string $action Optional action name
 * @return bool True if valid, false otherwise
 */
function tep_csrf_validate_api($input_params, $action = 'api') {
    if (!tep_csrf_protection_enabled()) {
        return true;
    }
    
    // For API endpoints, we might accept tokens in different ways
    $valid = false;
    
    // Check for CSRF token in parameters
    if (isset($input_params['csrf_token'])) {
        $valid = tep_csrf_token_validate($input_params['csrf_token'], $action);
    }
    
    // Check for token in headers
    if (!$valid && isset($_SERVER['HTTP_X_CSRF_TOKEN'])) {
        $valid = tep_csrf_token_validate($_SERVER['HTTP_X_CSRF_TOKEN'], $action);
    }
    
    // Fallback to sessiontoken for backward compatibility
    if (!$valid && isset($input_params['formid'])) {
        global $sessiontoken;
        if (isset($sessiontoken) && $input_params['formid'] == $sessiontoken) {
            $valid = true;
        }
    }
    
    return $valid;
}

/**
 * Simple include-and-validate function for form processors
 * Usage: require_once('includes/functions/csrf_middleware.php'); tep_csrf_require_valid_token();
 * 
 * @param string $action Optional action name
 * @param string $redirect_url Optional redirect URL on failure
 */
function tep_csrf_require_valid_token($action = 'default', $redirect_url = '') {
    tep_csrf_validate_or_die($action, true, $redirect_url);
}

/**
 * Check if current request has valid CSRF token without dying
 * Useful for conditional processing
 * 
 * @param string $action Optional action name
 * @return bool True if valid, false otherwise
 */
function tep_csrf_check_token($action = 'default') {
    return tep_csrf_validate_or_die($action, false);
}

/**
 * Generate and return CSRF protection data for forms
 * Useful for dynamic form generation
 * 
 * @param string $action Optional action name
 * @return array Array with token data
 */
function tep_csrf_get_form_data($action = 'default') {
    if (!tep_csrf_protection_enabled()) {
        return array();
    }
    
    return array(
        'csrf_token' => tep_csrf_token($action),
        'csrf_field' => tep_csrf_token_field($action),
        'action' => $action
    );
}

/**
 * Middleware function to be called at the start of form processing scripts
 * Automatically detects the type of request and validates accordingly
 * 
 * @param string $action Optional action name
 * @param array $options Options array with keys: ajax, api, redirect_url, die_on_failure
 */
function tep_csrf_middleware($action = 'default', $options = array()) {
    // Default options
    $defaults = array(
        'ajax' => false,
        'api' => false,
        'redirect_url' => '',
        'die_on_failure' => true
    );
    
    $options = array_merge($defaults, $options);
    
    // Auto-detect AJAX requests
    if (!$options['ajax'] && isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
        strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        $options['ajax'] = true;
    }
    
    // Handle different request types
    if ($options['ajax']) {
        tep_csrf_validate_ajax($action);
    } elseif ($options['api']) {
        $input_params = array_merge($_GET, $_POST);
        if (!tep_csrf_validate_api($input_params, $action)) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(array('error' => 'Security validation failed'));
            exit;
        }
    } else {
        tep_csrf_validate_or_die($action, $options['die_on_failure'], $options['redirect_url']);
    }
}
