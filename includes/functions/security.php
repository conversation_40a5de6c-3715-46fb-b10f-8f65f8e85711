<?php
/*
  Enhanced CSRF Protection Functions
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2024 osCommerce

  Released under the GNU General Public License
*/

/**
 * Generate a cryptographically secure CSRF token
 * 
 * @param string $action Optional action name for action-specific tokens
 * @return string The generated CSRF token
 */
function tep_csrf_token_generate($action = 'default') {
    // Use PHP's cryptographically secure random bytes if available
    if (function_exists('random_bytes')) {
        $random_data = random_bytes(32);
    } elseif (function_exists('openssl_random_pseudo_bytes')) {
        $random_data = openssl_random_pseudo_bytes(32);
    } else {
        // Fallback for older PHP versions
        $random_data = '';
        for ($i = 0; $i < 32; $i++) {
            $random_data .= chr(mt_rand(0, 255));
        }
    }
    
    // Create token with timestamp and action
    $timestamp = time();
    $token_data = $action . '|' . $timestamp . '|' . base64_encode($random_data);
    
    // Hash the token data
    $token = hash('sha256', $token_data . session_id());
    
    return $token;
}

/**
 * Store CSRF token in session
 * 
 * @param string $token The token to store
 * @param string $action Optional action name
 * @param int $lifetime Token lifetime in seconds (default: 3600)
 */
function tep_csrf_token_store($token, $action = 'default', $lifetime = 3600) {
    if (!tep_session_is_registered('csrf_tokens')) {
        tep_session_register('csrf_tokens');
        $csrf_tokens = array();
    } else {
        global $csrf_tokens;
        if (!is_array($csrf_tokens)) {
            $csrf_tokens = array();
        }
    }
    
    // Clean expired tokens
    tep_csrf_token_cleanup();
    
    // Store token with expiration time
    $csrf_tokens[$action][$token] = time() + $lifetime;
}

/**
 * Validate CSRF token
 * 
 * @param string $token The token to validate
 * @param string $action Optional action name
 * @return bool True if token is valid, false otherwise
 */
function tep_csrf_token_validate($token, $action = 'default') {
    global $csrf_tokens;
    
    if (!tep_session_is_registered('csrf_tokens') || !is_array($csrf_tokens)) {
        return false;
    }
    
    // Check if action exists
    if (!isset($csrf_tokens[$action])) {
        return false;
    }
    
    // Check if token exists and is not expired
    if (isset($csrf_tokens[$action][$token])) {
        if ($csrf_tokens[$action][$token] > time()) {
            // Token is valid, remove it (one-time use)
            unset($csrf_tokens[$action][$token]);
            return true;
        } else {
            // Token expired, remove it
            unset($csrf_tokens[$action][$token]);
        }
    }
    
    return false;
}

/**
 * Clean up expired CSRF tokens
 */
function tep_csrf_token_cleanup() {
    global $csrf_tokens;
    
    if (!tep_session_is_registered('csrf_tokens') || !is_array($csrf_tokens)) {
        return;
    }
    
    $current_time = time();
    
    foreach ($csrf_tokens as $action => $tokens) {
        if (is_array($tokens)) {
            foreach ($tokens as $token => $expiry) {
                if ($expiry <= $current_time) {
                    unset($csrf_tokens[$action][$token]);
                }
            }
            
            // Remove empty action arrays
            if (empty($csrf_tokens[$action])) {
                unset($csrf_tokens[$action]);
            }
        }
    }
}

/**
 * Get a CSRF token for use in forms
 * 
 * @param string $action Optional action name
 * @return string The CSRF token
 */
function tep_csrf_token($action = 'default') {
    $token = tep_csrf_token_generate($action);
    tep_csrf_token_store($token, $action);
    return $token;
}

/**
 * Generate CSRF token hidden input field
 * 
 * @param string $action Optional action name
 * @param string $field_name Field name (default: csrf_token)
 * @return string HTML hidden input field
 */
function tep_csrf_token_field($action = 'default', $field_name = 'csrf_token') {
    $token = tep_csrf_token($action);
    return '<input type="hidden" name="' . tep_output_string($field_name) . '" value="' . tep_output_string($token) . '" />';
}

/**
 * Validate CSRF token from request data
 * 
 * @param array $data Request data ($_POST, $_GET, etc.)
 * @param string $action Optional action name
 * @param string $field_name Field name (default: csrf_token)
 * @return bool True if valid, false otherwise
 */
function tep_csrf_validate_request($data, $action = 'default', $field_name = 'csrf_token') {
    if (!isset($data[$field_name])) {
        return false;
    }
    
    return tep_csrf_token_validate($data[$field_name], $action);
}

/**
 * Check if CSRF protection should be enforced
 * Can be disabled for testing or specific scenarios
 * 
 * @return bool True if CSRF should be enforced
 */
function tep_csrf_protection_enabled() {
    // Check if CSRF protection is disabled via configuration
    if (defined('CSRF_PROTECTION') && CSRF_PROTECTION == 'False') {
        return false;
    }
    
    // Default to enabled
    return true;
}

/**
 * Backward compatibility function for existing sessiontoken validation
 * This allows existing code to continue working while new code uses CSRF tokens
 * 
 * @param array $data Request data
 * @return bool True if valid sessiontoken or CSRF token
 */
function tep_validate_form_token($data) {
    global $sessiontoken;
    
    // First check new CSRF token
    if (tep_csrf_protection_enabled() && isset($data['csrf_token'])) {
        return tep_csrf_validate_request($data);
    }
    
    // Fallback to old sessiontoken validation
    if (isset($data['formid']) && isset($sessiontoken) && $data['formid'] == $sessiontoken) {
        return true;
    }
    
    return false;
}

/**
 * Generate meta tag for CSRF token (useful for AJAX requests)
 * 
 * @param string $action Optional action name
 * @return string HTML meta tag
 */
function tep_csrf_meta_tag($action = 'default') {
    $token = tep_csrf_token($action);
    return '<meta name="csrf-token" content="' . tep_output_string($token) . '" />';
}

/**
 * Get CSRF token for JavaScript/AJAX use
 * 
 * @param string $action Optional action name
 * @return array Token data for JSON encoding
 */
function tep_csrf_token_for_js($action = 'default') {
    return array(
        'token' => tep_csrf_token($action),
        'field_name' => 'csrf_token',
        'action' => $action
    );
}
