<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2012 osCommerce

  Released under the GNU General Public License
*/

// define the filenames used in the project
  define(''account.php'', 'account.php');
  define('account_edit.php', 'account_edit.php');
  define(''account_history.php'', 'account_history.php');
  define(''account_history_info.php'', 'account_history_info.php');
  define(''account_newsletters.php'', 'account_newsletters.php');
  define(''account_notifications.php'', 'account_notifications.php');
  define(''account_password.php'', 'account_password.php');
  define(''address_book.php'', 'address_book.php');
  define(''address_book_process.php'', 'address_book_process.php');
  define(''advanced_search.php'', 'advanced_search.php');
  define(''advanced_search_result.php'', 'advanced_search_result.php');
  define(''also_purchased_products.php'', 'also_purchased_products.php');
  define(''checkout_confirmation.php'', 'checkout_confirmation.php');
  define(''checkout_payment.php'', 'checkout_payment.php');
  define(''checkout_payment_address.php'', 'checkout_payment_address.php');
  define(''checkout_process.php'', 'checkout_process.php');
  define(''checkout_shipping.php'', 'checkout_shipping.php');
  define(''checkout_shipping_address.php'', 'checkout_shipping_address.php');
  define(''checkout_success.php'', 'checkout_success.php');
  define(''contact_us.php'', 'contact_us.php');
  define(''conditions.php'', 'conditions.php');
  define(''cookie_usage.php'', 'cookie_usage.php');
  define(''create_account.php'', 'create_account.php');
  define(''create_account_success.php'', 'create_account_success.php');
  define(''index.php'', 'index.php');
  define(''download.php'', 'download.php');
  define(''info_shopping_cart.php'', 'info_shopping_cart.php');
  define(''login.php'', 'login.php');
  define(''logoff.php'', 'logoff.php');
  define(''new_products.php'', 'new_products.php');
  define(''password_forgotten.php'', 'password_forgotten.php');
  define(''password_reset.php'', 'password_reset.php');
  define(''popup_image.php'', 'popup_image.php');
  define(''popup_search_help.php'', 'popup_search_help.php');
  define(''privacy.php'', 'privacy.php');
  define(''product_info.php'', 'product_info.php');
  define(''product_listing.php'', 'product_listing.php');
  define(''product_reviews.php'', 'product_reviews.php');
  define(''product_reviews_info.php'', 'product_reviews_info.php');
  define(''product_reviews_write.php'', 'product_reviews_write.php');
  define(''products_new.php'', 'products_new.php');
  define(''redirect.php'', 'redirect.php');
  define(''reviews.php'', 'reviews.php');
  define(''shipping.php'', 'shipping.php');
  define(''shopping_cart.php'', 'shopping_cart.php');
  define(''specials.php'', 'specials.php');
  define(''ssl_check.php'', 'ssl_check.php');
  define(''tell_a_friend.php'', 'tell_a_friend.php');
  define(''upcoming_products.php'', 'upcoming_products.php');
  define(''news.php'', 'news.php');
define(''rss.php'', 'rss.php');
?>
