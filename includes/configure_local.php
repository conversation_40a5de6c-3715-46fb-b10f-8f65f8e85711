<?php
/*
  Local Development Configuration
  Copy of configure.php modified for local XAMPP environment
*/

// Define the webserver and path parameters for local development
// * DIR_FS_* = Filesystem directories (local/physical)
// * DIR_WS_* = Webserver directories (virtual/URL)

// Local development URLs
define('HTTP_SERVER', 'http://localhost.cadservices');
define('HTTPS_SERVER', 'https://localhost.cadservices');
define('ENABLE_SSL', true);
define('HTTP_COOKIE_DOMAIN', '');
define('HTTPS_COOKIE_DOMAIN', '');
define('HTTP_COOKIE_PATH', '/');
define('HTTPS_COOKIE_PATH', '/');
define('DIR_WS_HTTP_CATALOG', '/');
define('DIR_WS_HTTPS_CATALOG', '/');

// Local filesystem paths
define('DIR_FS_CACHE', 'E:/Build/httpdocs/temp/');
define('DIR_FS_CATALOG', 'E:/Build/httpdocs/');
define('DIR_FS_DOWNLOAD', DIR_FS_CATALOG . 'download/');
define('DIR_FS_DOWNLOAD_PUBLIC', DIR_FS_CATALOG . 'pub/');

// Local MySQL database configuration (XAMPP default)
define('DB_SERVER', '127.0.0.1');
define('DB_SERVER_USERNAME', 'root');
define('DB_SERVER_PASSWORD', ''); // XAMPP default has no password
define('DB_DATABASE', 'cadservices_local');
define('USE_PCONNECT', 'false');
define('STORE_SESSIONS', '');
define('CFG_TIME_ZONE', 'UTC');

// Allow all IPs for local development
define('ALLOWED_ADMIN_IP_ADDRESSES', ['127.0.0.1', '::1', 'localhost']);

?>
