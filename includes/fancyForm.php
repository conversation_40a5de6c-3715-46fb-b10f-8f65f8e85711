<?php

	$fancyFormName = "";
	$fancyFormPhone = "";
	$fancyFormEmail = "";
	$products_name_text = "";
	$fancyFormProduct = "";
	$fancyFormEnquiry = "";
	$queryFrmextra = "";
	
	if (isset($_POST['queryFrmextra'])) $queryFrmextra = $_POST['queryFrmextra'];
	if (!isset( $products_name_text ) && isset($listing['products_name']) && isset($products_name)) $fancyFormProduct = $products_name . $listing['products_name'];
	if (isset($_POST['queryFrmEnquiry'])) $fancyFormEnquiry = $_POST['queryFrmEnquiry'];
	if (isset($_POST['queryFrmQueryFrmName'])) $fancyFormName = $_POST['queryFrmName'];
	if (isset($_POST['queryFrmEnquiry'])) $fancyFormPhone = $_POST['queryFrmPhone'];
	if (isset($_POST['queryFrmEmail'])) $fancyFormEmail = $_POST['queryFrmEmail'];
	if (isset($_POST['queryFrmProduct'])) $fancyFormProduct = $_POST['queryFrmProduct'];
	
	if (tep_session_is_registered('customer_id')) {
		$account_query = tep_db_query("select customers_gender, customers_firstname, customers_lastname, customers_dob, customers_email_address, customers_telephone, customers_fax from " . TABLE_CUSTOMERS . " where customers_id = '" . (int)$customer_id . "'");
		$account = tep_db_fetch_array($account_query);

		if (isset($_POST['queryFrmQueryFrmName'])){
			$fancyFormName = $_POST['queryFrmName'];
		} else {
			$fancyFormName = $account['customers_firstname'] . ' ' . $account['customers_lastname'];
		}
		if (isset($_POST['queryFrmEnquiry'])){
			$fancyFormPhone = $_POST['queryFrmPhone'];        
		} else {
			$fancyFormPhone = $account['customers_telephone'];
		}
		if (isset($_POST['queryFrmEmail'])){
			$fancyFormEmail = $_POST['queryFrmEmail'];        
		} else {
			$fancyFormEmail = $account['customers_email_address'];
		}
		if (isset($_POST['queryFrmProduct'])) $products_name_text = $_POST['queryFrmProduct'];			
	}
?>
<script type="text/javascript">$( document ).ready(function() {var frm = $('#fancyFormForm');frm.submit(function (ev) {$.ajax({type: frm.attr('method'),url: frm.attr('action'),data: frm.serialize(),beforeSend: function(data){$('#fancyFormSpinner').show();var thisModal = $('#fancyForm');if ($('#queryFrmextra').val().replace(" ", "").toLowerCase() == 'ybzq'){     $('#extraError').hide();thisModal.modal('hide');var notiModal = $('#notificationModal');notiModal.find('#noti-modal-title').html('Quotation Request Submitted');notiModal.find('#noti-modal-body').html('<strong><p class="text-center">Thank-you for your request,<br> A member of our sales team will be in touch shortly.</p></strong>');notiModal.modal('show');frm.trigger("reset");}else{$('#extraError').fadeOut('slow').fadeIn('slow');return false;}},success: function (data) {}});ev.preventDefault();});$('#fancyForm').on('show.bs.modal', function (event) {var button = $(event.relatedTarget);var recipient = button.data('product');var modal = $(this);modal.find('#queryFrmProduct').val(recipient);})});</script>
<?php echo tep_draw_form('fancyform', tep_href_link('quoteRequestConfirmation.php', 'action=quote'), 'post', 'id="fancyFormForm"', true); ?>
   <div class="modal fade" id="fancyForm" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content" id="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <span class="modal-title" id="myModalLabel">Quotation Request</span>  </div>
      <div class="modal-body" id="modal-body">
	<p>Please provide information as requested below to receive an official quotation.</p>
   <?php echo 'Name'; ?>
     <?php echo tep_draw_input_field('queryFrmName',$fancyFormName,'size="50"'); 
	  echo 'E-Mail';  echo tep_draw_input_field('queryFrmEmail',$fancyFormEmail,'size="50"');
      echo 'Phone'; echo tep_draw_input_field('queryFrmPhone',$fancyFormPhone,'size="50"'); 
      echo 'Item';  echo tep_draw_input_field('queryFrmProduct',$fancyFormProduct,'size="50" id="queryFrmProduct"');
      echo '<strong>Any additional items, comments or questions:</strong>';
      echo tep_draw_textarea_field('queryFrmEnquiry','','','',$fancyFormEnquiry); ?>
    <div style="margin-left:25%;text-align: center;width:50%">
        <img align="center" src="images/extra_gdrt.png" alt="Capture XYBZQX remove first and last letter" style="display: block;margin-left: auto;margin-right: auto">
        Please input the text above<br /> 
        <span id="extraError" style="color:red;font-weight:Bolder;display:<?php if ( isset($extraCorrect) && $extraCorrect != 1 ){echo 'block';} else { echo 'none';}; ?>">
            Text entered does not match text displayed, (Note: case and spacing ignored)
        </span><?php
        echo tep_draw_input_field("queryFrmextra",strtolower(str_replace(' ','',$queryFrmextra)),'size="8" id="queryFrmextra"');
        /*<input name="extra" type="text" id="extra" size="8" value="<?php echo  ?>"></p>*/ ?>
	</div>	
    </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        
       	<button type="submit" class="btn btn-primary"><i class="fa fa-spinner fa-spin" id="fancyFormSpinner" style="display:none;"></i> Send</button>
      </div>
  </div>
</div></div></form>

<div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-labelledby="Notification">
    <div class="modal-dialog" role="document">
        <div class="modal-content" id="noti-modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="modal-title" id="noti-modal-title"></div>
            </div>
            <div class="modal-body" id="noti-modal-body">


            </div>	        
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


