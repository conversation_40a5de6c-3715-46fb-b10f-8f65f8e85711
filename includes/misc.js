var equal_height = $(".equal-height");
var selected;
var cc = $.cookie('grid_list');
function equalHeight(group, resize) {
    var resize = resize || false;
    var maxHeight = 0;
    clearTimeout(timer);
    var timer = setTimeout(function() { // need to set a slight delay
        if (resize) {
            group.height('auto'); // need this for window resize
        }
        group.each(function() {
            if ($(this).height() > maxHeight) {
                maxHeight = $(this).height();
            }
        });
        group.height(maxHeight);
    }, 10);
}

function swapNodes(a, b) {
    a = $(a); b = $(b);    
    var tmp = $('<span>').hide();
    
    a.before(tmp);
    a.css("width","20%");
    b.css("width","100%");
    b.before(a);
    tmp.replaceWith(b);
};

function autoHeight(group) {
    group.height('auto');
}
// takes the form field value and returns true on valid number
function valid_credit_card(value) {
    // accept only digits, dashes or spaces
    if (/[^0-9-\s]+/.test(value)) return false;

    // The Luhn Algorithm. It's so pretty.
    var nCheck = 0,
        nDigit = 0,
        bEven = false;
    value = value.replace(/\D/g, "");
    if ((value == '') || isNaN(value) || (value.length < 6)) return false;
    for (var n = value.length - 1; n >= 0; n--) {
        var cDigit = value.charAt(n),
            nDigit = parseInt(cDigit, 10);

        if (bEven) {
            if ((nDigit *= 2) > 9) nDigit -= 9;
        }

        nCheck += nDigit;
        bEven = !bEven;
    }
    return (nCheck % 10) == 0;
}

function setAccord(id) {
    return $("#paymentAccordian").accordion("option", "active", id);
}


$(window).resize(function() { // corrects each box size when resizing window otherwise boxes will be too small or too large
    if (!$('#products .item').hasClass('list-group-item')) { // don't adjust when in list view
        equalHeight(equal_height, true);
    }
});

function htmlDecode(value){
return $('<div/>').html(value).text();
}

function validateCcOwner(owner) {
		return (ccOwner == '');
	}



$(document).ready(function() {
	var $cartDropdown = $("#navbar_right");

	// Initialize the popover once on page load
	$cartDropdown.popover({
		trigger: "manual", // We'll manually trigger it
		placement: "bottom",
		content: "", // Content will be set before showing
		title: "Added to cart"
	}).on("show.bs.popover", function() {
		$(this).data("bs.popover").tip().css("max-width", "600px");
	});

	// Listen for the custom event triggered by the HTMX request
	document.body.addEventListener('cartUpdate', function(event) {
		const productName = event.detail.productName;

		// Update the popover content and show it
		$cartDropdown.data('bs.popover').options.content = productName;
		$cartDropdown.popover('show');

		// Hide the popover after a delay
		clearTimeout(window.popover_timeout); // Clear any previous timeout
		window.popover_timeout = setTimeout(function() {
			$cartDropdown.popover('hide');
		}, 2000);
	});

		

		/*$cartDropdown.on("shown.bs.popover", function(){  			
        var popoverId = $(this).attr('aria-describedby');
        var popoverElement = $('#' + popoverId);
        var position = popoverElement.offset();
        var width = popoverElement.innerWidth();
        var height = popoverElement.innerHeight();

        // Call fireworks function with position data
       // startFireworks(position, width, height);
		});*/
		if (cc == 'grid') {
			$('#products .item').removeClass('list-group-item').addClass('grid-group-item');
		} else {
			$('#products .item').removeClass('grid-group-item').addClass('list-group-item');
		}    
		if (!$('#products .item').hasClass('list-group-item')) { // don't adjust when in list view
			equalHeight(equal_height);
		}
		$("#list").click(function(e) {
			e.preventDefault();
			autoHeight(equal_height); // reset heights to auto for list view
		});

		$("#grid").click(function(e) {
			e.preventDefault();
			equalHeight(equal_height); // adjust heights in grid view
		});
			$('#grid').click(function(event) {
				event.preventDefault();
				$('#products .item').addClass('grid-group-item').removeClass('list-group-item');
				$.cookie('grid_list', 'grid');
			});
			$('#list').click(function(event) {
				event.preventDefault();
				$('#products .item').removeClass('grid-group-item').addClass('list-group-item');
				$.cookie('grid_list', 'list');
			});
		$("#paymentAccordian").accordion({
			header: ".accordHeader",
			collapsible: true,
			active: false,
			beforeActivate: function(event, ui) {
				aqrc = $(this).accordion('option', 'active');
				fges = $(this).accordion;
				dff = $('#cc_number').val();
				if (($(this).accordion('option', 'active') == 0) && ($('#cc_number').val() != '')) {
					$('#paymentMethodChangeModal').modal('toggle');
				}
			},
			activate: function(event, ui) {
				$("#paymentItem" + $(this).accordion('option', 'active')).prop('checked', true);
			}
		});
		$("#keepCCBtn").click(function(event, ui) {
			return setAccord(0);
		})

		

		$("#paymentMethodsForm").submit(function(event, ui) {
			var temp1 = $("#paymentItem0").prop("checked")
			if (temp1 == true) {
				var ccOwner = $('#cc_owner').val();
				var ccCVV = $('#cc_cvv').val();
				var ccNumber = $('#cc_number').val();
				var ccType = $('#cc_type').prop("selectedIndex");
				var ccExpiresMonth = $('#cc_expires_month').val();
				var ccExpiresMonthVali = ((ccExpiresMonth > 0) && (ccExpiresMonth < 13));
				var ccExpiresYear = $('#cc_expires_year').val();
				var ccExpiresYearVali = ccExpiresYear > 0;
				if (ccExpiresMonthVali && ccExpiresYearVali) {
					var currDate = new Date();
					var cardDate = new Date(parseInt(ccExpiresYear) + 2000, ccExpiresMonth); // we are buggered after 2099
					var currDategetTime = currDate.getTime();
					var cardDategetTime = cardDate.getTime()
					var ccExpiresVali = (cardDate.getTime() > currDate.getTime());
				} else {
					var ccExpiresVali = true // no need to check this if month and/or year fails
				}
				var ccCvvVali = ccCVV.match(/[0-9][0-9][0-9]/);
				var ccIssue = $('#cc_issue').val();
				var isError = 0;
				var errorMsg = '<ul>';
				var creditValid = valid_credit_card(ccNumber);

				if (ccOwner == '') {
					isError = 1;
					errorMsg += '<li> Name on Card is empty </li>';
				}
				if (!creditValid) {
					isError = 1;
					errorMsg += '<li> Credit Card Number is not Valid </li>';
				}
				if (ccType == 0) {
					isError = 1;
					errorMsg += '<li> Card Type not selected </li>';
				}
				if (!ccExpiresVali || !ccExpiresMonthVali || !ccExpiresYearVali) {
					isError = 1;
					errorMsg += '<li> Credit Card Expiry date is not valid. </li>';
					errorMsg += '<ul>';
					if (!ccExpiresMonthVali) {
						isError = 1;
						errorMsg += '<li> Credit expiry month is not Valid </li>';
					}
					if (!ccExpiresYearVali) {
						isError = 1;
						errorMsg += '<li> Credit expiry year is not Valid </li>';
					}
					if (!ccExpiresVali) {
						isError = 1;
						errorMsg += '<li> Credit expiry date is in the past </li>';
					}
					
					errorMsg += '</ul>';
				}
				if (!ccCvvVali) {
						isError = 1;
						errorMsg += '<li> CVV is not valid </li>';
					}
				errorMsg += '</ul>';
				if (isError) {
					event.preventDefault();
					$('#ccErrorModal').on('hidden.bs.modal', function(e) {
						$('#paymentError').fadeIn('5500').html(errorMsg);
					});
					$('#ccErrorModal').modal('toggle');
				}

			}
		});
		$("#cc_expires_month").keyup(function() {
			if (this.value.length == this.maxLength) {
				$("#cc_expires_year").focus();
			}
		});	
		$('ul.sf-menu').superfish({
			delay: 1000, // 1.0 second delay on mouseout
			onHandleTouch: function(a) {
			   $(this).css("visibility", "hidden");
			   return false;
			}
		});

		$('[data-toggle=offcanvas]').click(function() {
			if ($('.sidebar-offcanvas').css('background-color') == 'rgb(255, 255, 255)') {
				$('.list-group-item').attr('tabindex', '-1');
			} else {
				$('.list-group-item').attr('tabindex', '');
			}
			$('.row-offcanvas').toggleClass('active');
		});
		if (!$('#products .item').hasClass('list-group-item')) { // don't adjust when in list view
			equalHeight(equal_height, true);
		}
		var containers = $("[id^=relatedProductsModuleContainer]");
		var tableModules = $("[id^=relatedProductsModule]");
		for (let i = 0; i < containers.length; i++) {
			containerId = containers[i].id;
			if ( (containerId == "relatedProductsModuleContainer") || (containerId == "relatedProductsModuleContainer-0") ) {
				tableModule = $("#relatedProductsModule-0");
				$("#" + containerId).replaceWith(tableModule);
			} else {
				containerIdSplit = containerId.split("-");
				theID = containerIdSplit[1];
				tableModule = $("#relatedProductsModule-" + theID);
				$("#" + containerId).replaceWith(tableModule);
			}
		}
		$("#bluebeamlandingform").submit(function(event, ui) {
			var formName = $('#inputName').val();
			var formCompany = $('#inputCompany').val();
			var formTele = $('#inputTelephone').val();
			var formEmail = $('#inputEmail').val();
			var isError = 0;
			var errorMsg = 'The form has the following errors:<ul>';
			if (formName == '') {
				isError = 1;
				errorMsg += '<li> Name field is empty </li>';
			}
			if (formTele == '') {
				isError = 1;
				errorMsg += '<li> Phone number field is empty </li>';
			}
			if (formEmail == '') {
				isError = 1;
				errorMsg += '<li> E-mail field is empty </li>';
			}
			errorMsg += '</ul>';
			if (isError) {
				event.preventDefault();
				$('#formErrorModal').on('hidden.bs.modal', function(e) {
					$('#formError').fadeIn('5500').html(errorMsg);
				});
				$('#formErrorModal').modal('toggle');
			}
		});
		

		/*
	$(".btn-buy").click(function(e) {
		e.preventDefault();
		var $el = $(this);
		var productId = $el.data("products-id");
		if (this.dataset.hasOwnProperty('attributes')) {
			attribs = $el.data('attributes');
		}else{
		var attribs = decodeURI(window.location.href);
		var attribs_string = attribs.replace(/\.[^/.]+$/, '');
			//console.log(attribs_string);
		attribs = attribs_string.slice(attribs_string.indexOf("{"));	
		}
		// Make the API call to add the item to the cart
		
		if (typeof attribs !== 'undefined') attribs_url = "&attribs=" + encodeURI(attribs)
			else attribs_url = "";		
		$.get("api.php?addToCart=1&products_id=" + productId + attribs_url, function(response, status) {
			if (status == "success") {
				var data = JSON.parse(response); // Explicitly parse the JSON string

				// Update the cart count and total price in the dropdown
				$('#shopping_cart_dropdown_toggle').html('<i class="fa fa-shopping-cart"></i>&nbsp;' + data[0].new_cart_total_items + ' item(s) <span class="caret"></span>');
				$('#shopping_cart_dropdown li:first a').html(data[0].new_cart_total_items + ' item(s), £' + data[0].new_cart_total_price.toFixed(2));

				// Optionally, add the new item to the dropdown if not already listed
				var $existing_item = $('#shopping_cart_dropdown').find('a[href*="' + data[0].products_id + '"]');
				new_item_exists = $existing_item.length > 0;
				if (!new_item_exists) {
					// Insert new item into the dropdown menu
					$dividers = $('#shopping_cart_dropdown li.divider');
					$last_divider = $dividers.last();
					$last_divider.before('<li><a href="' + data[0].url + '">' + data[0].qty + ' x ' + data[0].Name + '</a></li>');
				} else {
					var oldText = $existing_item.text();
					var oldQty = parseInt(oldText.split(' x ')[0]);
					var newQty = oldQty + data[0].qty;
					$existing_item.text(newQty + ' x ' + data[0].Name);
				}
				
				// Show the update message in a popover
				var message = data[0].Name;
				$cartDropdown.data('bs.popover').options.content = message;
				$cartDropdown.popover('show');
				popover_timeout = setTimeout(function() {
					$cartDropdown.popover('hide');
				}, 2000);
				
			}
		});
	});	*/


	//shopping cart scripts
	var quantity_timeout;
	$(".cart_quantity_input").on('change', function() 	{
		
		var $el = $(this);
		var quantity = $el.val();
		var products_id = $el.data("products_id");		
		var price= $el.data("price");
		index = $(this).data('index');
		clearTimeout(quantity_timeout);
		products_subtotals = $('.products_subtotals');
		products_qtys = $('.cart_quantity_input');
		cart_subtotal = $('#cart_subtotal');
		
		products_subtotals.html('<i class="fa fa-spinner fa-spin fa-fw"></i><span class="sr-only">Updating...</span>');
		cart_subtotal.html('<i class="fa fa-spinner fa-spin fa-fw"></i><span class="sr-only">Updating...</span>');		
		quantity_timeout = setTimeout(function() {
			products = new Array;
			for (i = 0; i < products_qtys.length; i++){
				el = $(products_qtys[i]);
				id = el.data('products_id');
				qty = el.val();
				products.push({"id": id, "qty": qty});
			}		
			products_string = JSON.stringify(products);
			$.get("api.php?update_cart_quantity=1&products=" + products_string, function(response, status) {
				if (status == "success") {
					var data = JSON.parse(response); // Explicitly parse the JSON string					
					// Update the cart count and total price in the dropdown
					cart_subtotal.html(data[0]['new_cart_total_price']);
					for (i = 0; i < data[0]['products'].length; i++){
						if ($('#cart_quantity_input_' + i).data('products_id') == data[0]['products'][i]['id']) {
							$('#products_subtotal_' + i).html(data[0]['products'][i]['final_price']);
						}
						//$('#products_subtotal_' + $i).html(data[0]['products'][i]['products_price_total']);
					}
				}
			});
		}, 1000);
	});


});

// CSRF Token Management for JavaScript/AJAX
var CSRFManager = {
    // Get CSRF token from meta tag
    getToken: function(action) {
        action = action || 'default';
        var metaToken = $('meta[name="csrf-token"]').attr('content');
        if (metaToken) {
            return metaToken;
        }

        // Fallback: try to get token via AJAX
        return this.fetchToken(action);
    },

    // Fetch new CSRF token via AJAX
    fetchToken: function(action) {
        action = action || 'default';
        var token = null;

        $.ajax({
            url: 'api.php',
            method: 'GET',
            data: { getCSRFToken: action },
            async: false,
            success: function(response) {
                if (response && response.token) {
                    token = response.token;
                }
            }
        });

        return token;
    },

    // Add CSRF token to form data
    addToFormData: function(formData, action) {
        action = action || 'default';
        var token = this.getToken(action);
        if (token) {
            if (formData instanceof FormData) {
                formData.append('csrf_token', token);
            } else if (typeof formData === 'object') {
                formData.csrf_token = token;
            } else if (typeof formData === 'string') {
                formData += (formData ? '&' : '') + 'csrf_token=' + encodeURIComponent(token);
            }
        }
        return formData;
    },

    // Add CSRF token to form as hidden field
    addToForm: function(form, action) {
        action = action || 'default';
        var token = this.getToken(action);
        if (token) {
            var $form = $(form);
            // Remove existing CSRF token field
            $form.find('input[name="csrf_token"]').remove();
            // Add new token field
            $form.append('<input type="hidden" name="csrf_token" value="' + token + '" />');
        }
    },

    // Setup AJAX requests to automatically include CSRF tokens
    setupAjaxCSRF: function() {
        var self = this;

        // Setup AJAX beforeSend to add CSRF token
        $(document).ajaxSend(function(event, xhr, settings) {
            // Only add CSRF token to POST requests
            if (settings.type && settings.type.toLowerCase() === 'post') {
                var token = self.getToken();
                if (token) {
                    xhr.setRequestHeader('X-CSRF-Token', token);

                    // Also add to data if it's form data
                    if (settings.data) {
                        settings.data = self.addToFormData(settings.data);
                    }
                }
            }
        });

        // Handle CSRF validation errors
        $(document).ajaxError(function(event, xhr, settings) {
            if (xhr.status === 403 && xhr.responseJSON && xhr.responseJSON.code === 'CSRF_VALIDATION_FAILED') {
                console.warn('CSRF validation failed, refreshing token...');
                // Refresh the page or show error message
                if (confirm('Security validation failed. Please refresh the page and try again.')) {
                    location.reload();
                }
            }
        });
    }
};

// Initialize CSRF protection when document is ready
$(document).ready(function() {
    CSRFManager.setupAjaxCSRF();

    // Add CSRF tokens to all forms with class 'csrf-protected'
    $('form.csrf-protected').each(function() {
        CSRFManager.addToForm(this);
    });

    // Handle dynamic form submissions
    $(document).on('submit', 'form[data-csrf-action]', function(e) {
        var action = $(this).data('csrf-action');
        CSRFManager.addToForm(this, action);
    });
});
