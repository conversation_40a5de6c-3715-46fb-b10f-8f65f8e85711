<?php
/*
  Product Suffix Caching Script
  
  This script populates the cached_product_suffix column in the products_variations table
  for improved search performance.
  
  Run this script:
  1. Initially after adding the cached_product_suffix column
  2. Whenever product attributes are modified
  3. As a maintenance task if needed
*/

require('includes/application_top.php');

// Check if we're running from command line or web
$is_cli = (php_sapi_name() === 'cli');



echo "Starting product suffix caching...\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Check if the cached_product_suffix column exists
$check_column = tep_db_query("SHOW COLUMNS FROM products_variations LIKE 'cached_product_suffix'");
if (tep_db_num_rows($check_column) == 0) {
    echo "ERROR: cached_product_suffix column does not exist in products_variations table.\n";
    echo "Please run this SQL first:\n";
    echo "ALTER TABLE products_variations ADD COLUMN cached_product_suffix VARCHAR(255) DEFAULT NULL;\n";
    echo "CREATE INDEX idx_cached_product_suffix ON products_variations(cached_product_suffix);\n";
    exit(1);
}

// Get statistics before
$stats_before = tep_db_fetch_array(tep_db_query("
    SELECT 
        COUNT(*) as total_variations,
        COUNT(cached_product_suffix) as cached_count,
        COUNT(*) - COUNT(cached_product_suffix) as uncached_count
    FROM products_variations
"));

echo "Statistics before caching:\n";
echo "- Total variations: " . $stats_before['total_variations'] . "\n";
echo "- Already cached: " . $stats_before['cached_count'] . "\n";
echo "- Need caching: " . $stats_before['uncached_count'] . "\n\n";

// Perform the caching
$start_time = microtime(true);
$total_updated = tcs_product_attributes::cache_all_product_suffixes();
$end_time = microtime(true);

// Get statistics after
$stats_after = tep_db_fetch_array(tep_db_query("
    SELECT 
        COUNT(*) as total_variations,
        COUNT(cached_product_suffix) as cached_count,
        COUNT(*) - COUNT(cached_product_suffix) as uncached_count
    FROM products_variations
"));

echo "Caching completed!\n";
echo "- Variations processed: " . $total_updated . "\n";
echo "- Time taken: " . round($end_time - $start_time, 2) . " seconds\n\n";

echo "Statistics after caching:\n";
echo "- Total variations: " . $stats_after['total_variations'] . "\n";
echo "- Cached: " . $stats_after['cached_count'] . "\n";
echo "- Still uncached: " . $stats_after['uncached_count'] . "\n\n";

// Show some examples
echo "Sample cached suffixes:\n";
$sample_query = tep_db_query("
    SELECT pv.products_variations_id, pv.cached_product_suffix, pd.products_name
    FROM products_variations pv
    JOIN products p ON pv.products_id = p.products_id
    JOIN products_description pd ON p.products_id = pd.products_id
    WHERE pv.cached_product_suffix IS NOT NULL
    AND pd.language_id = 1
    LIMIT 10
");

while ($sample = tep_db_fetch_array($sample_query)) {
    echo "- " . $sample['products_name'] . " -> " . $sample['cached_product_suffix'] . "\n";
}

if (!$is_cli) {
    echo '</pre>';
    echo '<p><a href="categories.php">Back to Admin</a></p>';
}

echo "\nDone!\n";
?>
