@echo off
echo ========================================
echo Local Development Environment Setup
echo ========================================
echo.

echo This script will help you set up your local development environment
echo with XAMPP, MySQL, PHP, and Apache for your PHP application.
echo.

echo Current Configuration:
echo - XAMPP Path: E:\tools\xampp\
echo - PHP Path: E:\tools\php\
echo - Project Path: E:\Build\httpdocs\
echo.

echo Step 1: Checking XAMPP Installation...
if not exist "E:\tools\xampp\xampp-control.exe" (
    echo ERROR: XAMPP not found at E:\tools\xampp\
    echo Please verify your XAMPP installation path.
    pause
    exit /b 1
)
echo XAMPP found!

echo.
echo Step 2: Checking PHP Installation...
if not exist "E:\tools\php\php.exe" (
    echo WARNING: PHP not found at E:\tools\php\
    echo Will use XAMPP's built-in PHP instead.
) else (
    echo PHP found!
)

echo.
echo Step 3: Creating necessary directories...
if not exist "E:\Build\httpdocs\temp" mkdir "E:\Build\httpdocs\temp"
if not exist "E:\Build\httpdocs\download" mkdir "E:\Build\httpdocs\download"
if not exist "E:\Build\httpdocs\pub" mkdir "E:\Build\httpdocs\pub"
echo Directories created!

echo.
echo Step 4: Backing up original configuration...
if exist "E:\Build\httpdocs\includes\configure.php" (
    copy "E:\Build\httpdocs\includes\configure.php" "E:\Build\httpdocs\includes\configure.php.backup"
    echo Original configuration backed up!
)

echo.
echo ========================================
echo MANUAL STEPS REQUIRED:
echo ========================================
echo.
echo 1. ADD VIRTUAL HOST TO APACHE:
echo    - Open: E:\tools\xampp\apache\conf\extra\httpd-vhosts.conf
echo    - Add the contents of: xampp-vhost-config.conf
echo.
echo 2. ENABLE VIRTUAL HOSTS IN APACHE:
echo    - Open: E:\tools\xampp\apache\conf\httpd.conf
echo    - Uncomment line: Include conf/extra/httpd-vhosts.conf
echo.
echo 3. ADD HOST ENTRY:
echo    - Open: C:\Windows\System32\drivers\etc\hosts (as Administrator)
echo    - Add line: 127.0.0.1 localhost.cadservices
echo.
echo 4. GENERATE SSL CERTIFICATE:
echo    - Run XAMPP Control Panel as Administrator
echo    - Go to Apache Config ^> SSL Settings
echo    - Or use the SSL certificate generation commands below
echo.
echo 5. CREATE DATABASE:
echo    - Start XAMPP (Apache + MySQL)
echo    - Open phpMyAdmin: http://localhost/phpmyadmin
echo    - Create database: cadservices_local
echo    - Import your existing database structure/data
echo.
echo 6. UPDATE CONFIGURATION:
echo    - Copy includes\configure_local.php to includes\configure.php
echo    - Or modify includes\configure.php with local settings
echo.
echo 7. START XAMPP SERVICES:
echo    - Run: E:\tools\xampp\xampp-control.exe
echo    - Start Apache and MySQL services
echo.
echo 8. TEST YOUR SETUP:
echo    - Visit: https://localhost.cadservices
echo.
echo ========================================
echo Press any key to continue...
pause >nul
