<?php
require('includes/application_top.php');

$rowsq = tep_db_query("SELECT * FROM products_autodesk_catalog");
$rows = tep_db_fetch_all($rowsq);
foreach ($rows as $row) {
   $cooncoot = $row['offeringId'] . $row['intendedUsage_code'] . $row['accessModel_code'] . $row['servicePlan_code'] . $row['connectivity_code'] . $row['term_code'] . $row['orderAction'] . $row['specialProgramDiscount_code'] . '<br>';
   $new_hash = hash('crc32', $cooncoot);
   tep_db_query("UPDATE products_autodesk_catalog SET unique_hash = '" . $new_hash . "' WHERE unique_hash = '" . $row['unique_hash'] . "'");
   tep_db_query("UPDATE products_to_autodesk_catalog SET unique_hash = '" . $new_hash . "' WHERE unique_hash = '" . $row['unique_hash'] . "'");
}

//foreach (hash_algos() as $key => $hash) echo $hash . ': ' . hash($hash .  'offeringIdintendedUsagecodeaccessModelcodeservicePlancodeconnectivitycodetermcodeorderActionspecialProgramDiscountcode') . '<br>'; 
?>
