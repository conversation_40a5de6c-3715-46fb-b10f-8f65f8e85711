<?php
namespace api\modal;

require_once 'system/classes/ModalTabManager.php';

/**
 * Close the entire modal
 */
function close($params = []) {
    \ModalTabManager::clearTabs();
    
    // Return empty modal container (effectively hiding it)
    echo '<div id="modal_container" style="display: none;"></div>';
}

/**
 * Switch to a specific tab
 */
function switch_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::switchTab($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Close a specific tab
 */
function close_tab($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::closeTab($tabId);
    }
    
    $tabs = \ModalTabManager::getTabs();
    if (empty($tabs)) {
        // No tabs left, close modal
        header('HX-Trigger: closeModal');
        echo '<div id="tab-contents" role="tabpanel"><!-- No tabs --></div>';
    } else {
        echo \ModalTabManager::renderTabs();
    }
}

/**
 * Toggle pin status of a tab
 */
function toggle_pin($params = []) {
    $tabId = $params['tab_id'] ?? '';
    
    if ($tabId) {
        \ModalTabManager::togglePin($tabId);
    }
    
    echo \ModalTabManager::renderTabs();
}

/**
 * Open content in a new tab
 */
function open_content($params = []) {
    $title = $params['title'] ?? 'New Tab';
    $originalEndpoint = $params['original_endpoint'] ?? '';
    $url = $params['url'] ?? $originalEndpoint;

    // Fetch content from original endpoint
    $content = '';
    if ($originalEndpoint) {
        $content = fetchEndpointContent($originalEndpoint, $params);
    } else {
        $content = $params['content'] ?? '<p>No content provided</p>';
    }

    $result = \ModalTabManager::openContent($title, $content, $url);
    echo $result;
}

/**
 * Fetch content from an endpoint
 */
function fetchEndpointContent($endpoint, $params = []) {
    // Remove APP_ROOT from endpoint if present
    $endpoint = str_replace(APP_ROOT, '', $endpoint);
    $endpoint = ltrim($endpoint, '/');

    // Parse endpoint path
    $pathParts = explode('/', $endpoint);

    // Remove 'api' if it's the first part
    if ($pathParts[0] === 'api') {
        array_shift($pathParts);
    }

    if (count($pathParts) >= 2) {
        $module = $pathParts[0];
        $action = $pathParts[1];

        error_log("Looking for module: {$module}, action: {$action}");

        // Look for the API file in system/api
        $apiFile = "system/api/{$module}.api.php";
        error_log("Checking system API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            error_log("Trying function: " . $functionName);

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    $content = ob_get_clean();
                    error_log("Successfully got content, length: " . strlen($content));
                    return $content;
                } catch (Exception $e) {
                    ob_end_clean();
                    error_log("Error calling function: " . $e->getMessage());
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                error_log("Function {$functionName} does not exist");
            }
        } else {
            error_log("API file {$apiFile} does not exist");
        }

        // Try app-level API
        $apiFile = "api/{$module}.api.php";
        error_log("Checking app API file: " . $apiFile);

        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$module}\\{$action}";
            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }

        // Try direct file inclusion for non-API endpoints
        $viewFile = "system/views/{$module}/{$action}.edge.php";
        if (file_exists($viewFile)) {
            error_log("Found view file: " . $viewFile);
            ob_start();
            try {
                // Simple include - you might need to adjust this based on your view system
                include $viewFile;
                return ob_get_clean();
            } catch (Exception $e) {
                ob_end_clean();
                error_log("Error including view: " . $e->getMessage());
            }
        }
    }

    // Special handling for single-word endpoints like 'view'
    if (count($pathParts) === 1) {
        $action = $pathParts[0];

        // Try resources/api first (where view.api.php is located)
        $apiFile = "resources/api/{$action}.api.php";

        if (file_exists($apiFile)) {
            // Ensure required constants are defined for view.api.php
            if (!defined('FS_VIEWS')) {
                define('FS_VIEWS', 'resources/views');
            }
            if (!defined('FS_RESOURCES')) {
                define('FS_RESOURCES', 'resources');
            }
            if (!defined('FS_FUNCTIONS')) {
                define('FS_FUNCTIONS', 'resources/functions');
            }

            // Load the customers API file that view.api.php depends on
            $customersApiFile = 'resources/views/autodesk/customers/customers.api.php';
            if (file_exists($customersApiFile)) {
                require_once $customersApiFile;

                // Create an alias function to fix the namespace mismatch in view.api.php
                if (!function_exists('api\\customers\\view')) {
                    eval('
                        namespace api\\customers {
                            function view($p) {
                                return \\api\\autodesk\\customers\\view($p);
                            }
                        }
                    ');
                }
            }

            require_once $apiFile;

            // For view.api.php, the function is in the api namespace
            $functionName = "api\\{$action}";

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    $content = ob_get_clean();
                    return $content;
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                } catch (Error $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Fatal error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }

        // Try system API
        $apiFile = "system/api/{$action}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            // Try to find a default function
            $functionName = "api\\{$action}\\index";
            if (!function_exists($functionName)) {
                $functionName = "api\\{$action}\\{$action}";
            }

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }

        // Try app-level API
        $apiFile = "api/{$action}.api.php";
        if (file_exists($apiFile)) {
            require_once $apiFile;

            $functionName = "api\\{$action}\\index";
            if (!function_exists($functionName)) {
                $functionName = "api\\{$action}\\{$action}";
            }

            if (function_exists($functionName)) {
                ob_start();
                try {
                    $functionName($params);
                    return ob_get_clean();
                } catch (Exception $e) {
                    ob_end_clean();
                    return '<div class="p-4 text-red-600">Error calling function: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
        }
    }

    $errorMsg = 'Error: Could not load content from ' . htmlspecialchars($endpoint) . '. Available path parts: ' . implode(', ', $pathParts);
    error_log($errorMsg);
    return '<div class="p-4 text-red-600">' . $errorMsg . '</div>';
}

/**
 * Get current tab state for debugging
 */
function debug_tabs($params = []) {
    $tabs = \ModalTabManager::getTabs();

    echo '<pre>' . print_r($tabs, true) . '</pre>';
}

/**
 * Simple test endpoint to create a tab directly
 */
function test_tab($params = []) {
    $title = $params['title'] ?? 'Test Tab';
    $content = '
    <div class="p-6">
        <h2 class="text-xl font-bold mb-4">' . htmlspecialchars($title) . '</h2>
        <p class="mb-4">This is a test tab created directly without endpoint redirection.</p>
        <div class="bg-blue-50 p-4 rounded-lg">
            <p class="text-blue-800">✓ Tab system is working!</p>
        </div>
        <button
            type="button"
            class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
            hx-get="' . APP_ROOT . '/api/modal/test_tab"
            hx-vals=\'{"title": "Another Test Tab"}\'
            hx-target="#modal_tabs_container"
            hx-swap="innerHTML">
            Create Another Test Tab
        </button>
    </div>';

    echo \ModalTabManager::openContent($title, $content, '');
}
