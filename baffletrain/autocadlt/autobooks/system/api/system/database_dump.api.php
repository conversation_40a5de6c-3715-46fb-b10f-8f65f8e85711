<?php
namespace api\system\database_dump;

use system\users;
use system\database;

/**
 * Dump all tables in the database
 */
function dump_all_tables($p) {
    users::requireRole(['admin', 'dev']);
    
    try {
        $pdo = database::connection()->getPDO();
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $results = [];
        $total_tables = count($tables);
        
        foreach ($tables as $table) {
            $result = dump_single_table($table);
            $results[] = $result;
        }
        
        $success_count = count(array_filter($results, function($r) { return $r['success']; }));
        
        return render_dump_status([
            'type' => 'success',
            'title' => 'All Tables Dump Complete',
            'message' => "Successfully dumped {$success_count} of {$total_tables} tables to system/sql folder.",
            'details' => $results
        ]);
        
    } catch (Exception $e) {
        return render_dump_status([
            'type' => 'error',
            'title' => 'Dump Failed',
            'message' => 'Error dumping tables: ' . $e->getMessage()
        ]);
    }
}

/**
 * Dump only autobooks tables
 */
function dump_autobooks_tables($p) {
    users::requireRole(['admin', 'dev']);
    
    try {
        $pdo = database::connection()->getPDO();
        $stmt = $pdo->query("SHOW TABLES LIKE 'autobooks_%'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $results = [];
        $total_tables = count($tables);
        
        foreach ($tables as $table) {
            $result = dump_single_table($table);
            $results[] = $result;
        }
        
        $success_count = count(array_filter($results, function($r) { return $r['success']; }));
        
        return render_dump_status([
            'type' => 'success',
            'title' => 'Autobooks Tables Dump Complete',
            'message' => "Successfully dumped {$success_count} of {$total_tables} autobooks tables to system/sql folder.",
            'details' => $results
        ]);
        
    } catch (Exception $e) {
        return render_dump_status([
            'type' => 'error',
            'title' => 'Dump Failed',
            'message' => 'Error dumping autobooks tables: ' . $e->getMessage()
        ]);
    }
}

/**
 * Dump only autodesk tables
 */
function dump_autodesk_tables($p) {
    users::requireRole(['admin', 'dev']);
    
    try {
        $pdo = database::connection()->getPDO();
        $stmt = $pdo->query("SHOW TABLES LIKE 'autodesk_%'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $results = [];
        $total_tables = count($tables);
        
        foreach ($tables as $table) {
            $result = dump_single_table($table);
            $results[] = $result;
        }
        
        $success_count = count(array_filter($results, function($r) { return $r['success']; }));
        
        return render_dump_status([
            'type' => 'success',
            'title' => 'Autodesk Tables Dump Complete',
            'message' => "Successfully dumped {$success_count} of {$total_tables} autodesk tables to system/sql folder.",
            'details' => $results
        ]);
        
    } catch (Exception $e) {
        return render_dump_status([
            'type' => 'error',
            'title' => 'Dump Failed',
            'message' => 'Error dumping autodesk tables: ' . $e->getMessage()
        ]);
    }
}

/**
 * Dump selected tables
 */
function dump_selected_tables($p) {
    users::requireRole(['admin', 'dev']);
    
    $selected_tables = $p['tables'] ?? [];
    
    if (empty($selected_tables)) {
        return render_dump_status([
            'type' => 'error',
            'title' => 'No Tables Selected',
            'message' => 'Please select at least one table to dump.'
        ]);
    }
    
    try {
        $results = [];
        $total_tables = count($selected_tables);
        
        foreach ($selected_tables as $table) {
            $result = dump_single_table($table);
            $results[] = $result;
        }
        
        $success_count = count(array_filter($results, function($r) { return $r['success']; }));
        
        return render_dump_status([
            'type' => 'success',
            'title' => 'Selected Tables Dump Complete',
            'message' => "Successfully dumped {$success_count} of {$total_tables} selected tables to system/sql folder.",
            'details' => $results
        ]);
        
    } catch (Exception $e) {
        return render_dump_status([
            'type' => 'error',
            'title' => 'Dump Failed',
            'message' => 'Error dumping selected tables: ' . $e->getMessage()
        ]);
    }
}

/**
 * Delete a dump file
 */
function delete_dump($p) {
    users::requireRole(['admin', 'dev']);
    
    $filename = $p['filename'] ?? '';
    if (empty($filename)) {
        return '<tr><td colspan="4" class="px-6 py-4 text-center text-red-600">Invalid filename</td></tr>';
    }
    
    // Security check - only allow .sql files and prevent directory traversal
    if (!preg_match('/^[a-zA-Z0-9_.-]+\.sql$/', $filename)) {
        return '<tr><td colspan="4" class="px-6 py-4 text-center text-red-600">Invalid filename format</td></tr>';
    }
    
    $sql_folder = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'system' . DIRECTORY_SEPARATOR . 'sql';
    $file_path = $sql_folder . DIRECTORY_SEPARATOR . $filename;
    
    if (!file_exists($file_path)) {
        return '<tr><td colspan="4" class="px-6 py-4 text-center text-red-600">File not found</td></tr>';
    }
    
    if (unlink($file_path)) {
        return ''; // Empty response removes the table row
    } else {
        return '<tr><td colspan="4" class="px-6 py-4 text-center text-red-600">Failed to delete file</td></tr>';
    }
}

/**
 * Dump a single table to SQL file
 */
function dump_single_table($table_name) {
    try {
        $pdo = database::connection()->getPDO();
        $sql_folder = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'system' . DIRECTORY_SEPARATOR . 'sql';
        
        // Ensure sql folder exists
        if (!is_dir($sql_folder)) {
            mkdir($sql_folder, 0755, true);
        }
        
        $filename = "autobooks_table_{$table_name}.sql";
        $file_path = $sql_folder . DIRECTORY_SEPARATOR . $filename;
        
        // Get table structure
        $create_stmt = $pdo->query("SHOW CREATE TABLE `{$table_name}`");
        $create_result = $create_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Get table data
        $data_stmt = $pdo->query("SELECT * FROM `{$table_name}`");
        $data = $data_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Generate SQL content
        $sql_content = generate_table_sql($table_name, $create_result['Create Table'], $data);
        
        // Write to file
        $bytes_written = file_put_contents($file_path, $sql_content);
        
        if ($bytes_written === false) {
            return [
                'success' => false,
                'table' => $table_name,
                'message' => 'Failed to write file'
            ];
        }
        
        return [
            'success' => true,
            'table' => $table_name,
            'filename' => $filename,
            'size' => $bytes_written,
            'records' => count($data)
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'table' => $table_name,
            'message' => $e->getMessage()
        ];
    }
}

/**
 * Generate SQL content for a table
 */
function generate_table_sql($table_name, $create_table_sql, $data) {
    $sql = "-- phpMyAdmin SQL Dump\n";
    $sql .= "-- Generated by Autobooks Database Dump Manager\n";
    $sql .= "-- Generation Time: " . date('M d, Y \a\t h:i A') . "\n";
    $sql .= "-- Table: {$table_name}\n";
    $sql .= "-- Records: " . count($data) . "\n\n";
    
    $sql .= "SET SQL_MODE = \"NO_AUTO_VALUE_ON_ZERO\";\n";
    $sql .= "START TRANSACTION;\n";
    $sql .= "SET time_zone = \"+00:00\";\n\n";
    
    $sql .= "-- --------------------------------------------------------\n\n";
    $sql .= "-- Table structure for table `{$table_name}`\n--\n\n";
    $sql .= "DROP TABLE IF EXISTS `{$table_name}`;\n";
    $sql .= $create_table_sql . ";\n\n";
    
    if (!empty($data)) {
        $sql .= "-- Dumping data for table `{$table_name}`\n--\n\n";
        
        // Get column names
        $columns = array_keys($data[0]);
        $column_list = '`' . implode('`, `', $columns) . '`';
        
        $sql .= "INSERT INTO `{$table_name}` ({$column_list}) VALUES\n";
        
        $values = [];
        foreach ($data as $row) {
            $escaped_values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $escaped_values[] = 'NULL';
                } else {
                    $escaped_values[] = "'" . addslashes($value) . "'";
                }
            }
            $values[] = '(' . implode(', ', $escaped_values) . ')';
        }
        
        $sql .= implode(",\n", $values) . ";\n\n";
    }
    
    $sql .= "COMMIT;\n";
    
    return $sql;
}

/**
 * Render dump status message
 */
function render_dump_status($status) {
    $type_classes = [
        'success' => 'bg-green-50 border-green-200 text-green-800',
        'error' => 'bg-red-50 border-red-200 text-red-800',
        'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800'
    ];
    
    $icon_classes = [
        'success' => 'text-green-400',
        'error' => 'text-red-400',
        'warning' => 'text-yellow-400'
    ];
    
    $class = $type_classes[$status['type']] ?? $type_classes['error'];
    $icon_class = $icon_classes[$status['type']] ?? $icon_classes['error'];
    
    $html = "<div class=\"p-4 border rounded-md {$class}\">";
    $html .= "<div class=\"flex\">";
    $html .= "<div class=\"flex-shrink-0\">";
    
    if ($status['type'] === 'success') {
        $html .= "<svg class=\"h-5 w-5 {$icon_class}\" fill=\"currentColor\" viewBox=\"0 0 20 20\">";
        $html .= "<path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>";
        $html .= "</svg>";
    } else {
        $html .= "<svg class=\"h-5 w-5 {$icon_class}\" fill=\"currentColor\" viewBox=\"0 0 20 20\">";
        $html .= "<path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>";
        $html .= "</svg>";
    }
    
    $html .= "</div>";
    $html .= "<div class=\"ml-3\">";
    $html .= "<h3 class=\"text-sm font-medium\">" . htmlspecialchars($status['title']) . "</h3>";
    $html .= "<div class=\"mt-2 text-sm\">" . htmlspecialchars($status['message']) . "</div>";
    
    if (isset($status['details']) && !empty($status['details'])) {
        $html .= "<div class=\"mt-3\">";
        $html .= "<details class=\"cursor-pointer\">";
        $html .= "<summary class=\"text-sm font-medium\">View Details</summary>";
        $html .= "<div class=\"mt-2 max-h-40 overflow-y-auto\">";
        $html .= "<table class=\"min-w-full text-xs\">";
        $html .= "<thead><tr><th class=\"text-left pr-4\">Table</th><th class=\"text-left pr-4\">Status</th><th class=\"text-left pr-4\">Records</th><th class=\"text-left\">Size</th></tr></thead>";
        $html .= "<tbody>";
        
        foreach ($status['details'] as $detail) {
            $status_icon = $detail['success'] ? '✅' : '❌';
            $records = $detail['records'] ?? 'N/A';
            $size = isset($detail['size']) ? formatFileSize($detail['size']) : 'N/A';
            $message = $detail['success'] ? 'Success' : ($detail['message'] ?? 'Failed');
            
            $html .= "<tr>";
            $html .= "<td class=\"pr-4\">" . htmlspecialchars($detail['table']) . "</td>";
            $html .= "<td class=\"pr-4\">{$status_icon} {$message}</td>";
            $html .= "<td class=\"pr-4\">{$records}</td>";
            $html .= "<td>{$size}</td>";
            $html .= "</tr>";
        }
        
        $html .= "</tbody></table>";
        $html .= "</div>";
        $html .= "</details>";
        $html .= "</div>";
    }
    
    $html .= "</div>";
    $html .= "</div>";
    $html .= "</div>";
    
    // Add refresh button to reload the existing dumps list
    $html .= "<div class=\"mt-4\">";
    $html .= "<button hx-get=\"" . APP_ROOT . "/system/database_dump\" hx-target=\"#content_wrapper\" class=\"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500\">";
    $html .= "<svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">";
    $html .= "<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>";
    $html .= "</svg>";
    $html .= "Refresh Page";
    $html .= "</button>";
    $html .= "</div>";
    
    return $html;
}

/**
 * Helper function to format file size
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
