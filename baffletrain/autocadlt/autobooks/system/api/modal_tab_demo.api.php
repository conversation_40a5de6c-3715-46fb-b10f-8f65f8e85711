<?php
namespace api\modal_tab_demo;
use edge\Edge;

/**
 * Demo tab with simple content
 */
function demo_tab($p) {
    $tabTitle = $_SERVER['HTTP_X_TAB_TITLE'] ?? 'Demo Tab';
    $modalState = $_SERVER['HTTP_X_MODAL_STATE'] ?? 'closed';
    $hasPinnedTabs = $_SERVER['HTTP_X_HAS_PINNED_TABS'] ?? 'false';
    
    // Generate unique tab ID
    $tabId = 'demo_tab_' . uniqid();
    
    // Set response headers for tab management
    header('X-Tab-Title: ' . $tabTitle);
    header('X-Tab-Id: ' . $tabId);
    
    // Determine behavior based on modal state and pinned tabs
    $shouldCreateTab = ($modalState === 'closed') || ($modalState === 'open' && $hasPinnedTabs === 'false') || ($modalState === 'open');
    
    if ($shouldCreateTab) {
        // Return content that will be handled by the tab system
        echo '<div class="p-6">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h2 class="text-xl font-bold text-gray-900">Demo Tab Content</h2>
                    <p class="text-sm text-gray-600">Tab ID: ' . $tabId . '</p>
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-blue-800 font-semibold mb-2">Tab Information</h3>
                    <ul class="text-blue-700 text-sm space-y-1">
                        <li><strong>Title:</strong> ' . htmlspecialchars($tabTitle) . '</li>
                        <li><strong>Modal State:</strong> ' . $modalState . '</li>
                        <li><strong>Has Pinned Tabs:</strong> ' . $hasPinnedTabs . '</li>
                        <li><strong>Created:</strong> ' . date('Y-m-d H:i:s') . '</li>
                    </ul>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="font-semibold mb-2">Sample Content</h3>
                    <p class="text-gray-700 mb-3">This is a demonstration of the tab functionality in the system-wide modal. You can:</p>
                    <ul class="list-disc list-inside text-gray-600 space-y-1">
                        <li>Click the pin icon to pin this tab</li>
                        <li>Click the X to close this tab</li>
                        <li>Open more tabs to see the tab bar in action</li>
                        <li>Use keyboard shortcuts (Ctrl+W to close, Ctrl+1-9 to switch)</li>
                    </ul>
                </div>
                
                <div class="flex space-x-3">
                    <button 
                        type="button"
                        hx-get="' . APP_ROOT . '/api/modal_tab_demo/demo_tab"
                        hx-target="#modal_body"
                        hx-swap="innerHTML"
                        data-tab-title="Another Demo Tab"
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Open Another Tab
                    </button>
                    
                    <button 
                        type="button"
                        hx-get="' . APP_ROOT . '/api/modal_tab_demo/form_tab"
                        hx-target="#modal_body"
                        hx-swap="innerHTML"
                        data-tab-title="Form Tab"
                        class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Open Form Tab
                    </button>
                </div>
            </div>
        </div>';
    }
}

/**
 * Form tab with interactive content
 */
function form_tab($p) {
    $tabTitle = $_SERVER['HTTP_X_TAB_TITLE'] ?? 'Form Tab';
    $modalState = $_SERVER['HTTP_X_MODAL_STATE'] ?? 'closed';
    $hasPinnedTabs = $_SERVER['HTTP_X_HAS_PINNED_TABS'] ?? 'false';
    
    // Generate unique tab ID
    $tabId = 'form_tab_' . uniqid();
    
    // Set response headers for tab management
    header('X-Tab-Title: ' . $tabTitle);
    header('X-Tab-Id: ' . $tabId);
    
    echo '<div class="p-6">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
            </div>
            <div class="ml-3">
                <h2 class="text-xl font-bold text-gray-900">Form Example</h2>
                <p class="text-sm text-gray-600">Interactive form within a tab</p>
            </div>
        </div>
        
        <form hx-post="' . APP_ROOT . '/api/modal_tab_demo/submit_form" hx-target="#form_result" class="space-y-4">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                <input type="text" id="name" name="name" required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                <input type="email" id="email" name="email" required
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                <textarea id="message" name="message" rows="3" required
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
            </div>
            
            <div class="flex space-x-3">
                <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    Submit Form
                </button>
                
                <button type="button"
                        hx-get="' . APP_ROOT . '/api/modal_tab_demo/demo_tab"
                        hx-target="#modal_body"
                        hx-swap="innerHTML"
                        data-tab-title="Demo Tab"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Open Demo Tab
                </button>
            </div>
        </form>
        
        <div id="form_result" class="mt-4">
            <!-- Form submission results will appear here -->
        </div>
        
        <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 class="text-yellow-800 font-semibold mb-2">Form Tab Features</h3>
            <ul class="text-yellow-700 text-sm space-y-1">
                <li>• Form submissions stay within the tab</li>
                <li>• Tab state is preserved during form interactions</li>
                <li>• HTMX handles form submission without page reload</li>
                <li>• Multiple form tabs can be open simultaneously</li>
            </ul>
        </div>
    </div>';
}

/**
 * Handle form submission
 */
function submit_form($p) {
    $name = htmlspecialchars($p['name'] ?? '');
    $email = htmlspecialchars($p['email'] ?? '');
    $message = htmlspecialchars($p['message'] ?? '');
    
    // Simulate processing delay
    usleep(500000); // 0.5 seconds
    
    echo '<div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Form Submitted Successfully!</h3>
                <div class="mt-2 text-sm text-green-700">
                    <p><strong>Name:</strong> ' . $name . '</p>
                    <p><strong>Email:</strong> ' . $email . '</p>
                    <p><strong>Message:</strong> ' . $message . '</p>
                    <p class="mt-2"><em>Submitted at: ' . date('Y-m-d H:i:s') . '</em></p>
                </div>
            </div>
        </div>
    </div>';
}
