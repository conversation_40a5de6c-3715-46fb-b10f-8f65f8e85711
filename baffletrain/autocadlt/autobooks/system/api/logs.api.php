<?php
use system\users;

// Ensure only admin/dev can access this API
users::requireRole('admin');

// Set the path to the logs directory
$logsDir = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'logs/';

// Handle API requests
$action = $_POST['action'] ?? '';

switch ($action) {
    case 'clear_log':
        // Clear a log file
        if (isset($_POST['file']) && !empty($_POST['file'])) {
            $logFile = sanitizeFileName($_POST['file']);
            $filePath = $logsDir . $logFile;
            
            if (file_exists($filePath) && is_file($filePath)) {
                // Clear the file by writing an empty string to it
                file_put_contents($filePath, '');
                echo json_encode(['success' => true, 'message' => "Log file '$logFile' has been cleared."]);
            } else {
                echo json_encode(['success' => false, 'message' => "Error: Log file '$logFile' not found."]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => "Error: No log file specified."]);
        }
        break;
        
    case 'get_log_entries':
        // Get log entries with pagination and search
        if (isset($_POST['file']) && !empty($_POST['file'])) {
            $logFile = sanitizeFileName($_POST['file']);
            $filePath = $logsDir . $logFile;
            
            if (file_exists($filePath) && is_file($filePath)) {
                $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
                $perPage = isset($_POST['per_page']) ? max(10, intval($_POST['per_page'])) : 100;
                $search = isset($_POST['search']) ? trim($_POST['search']) : '';
                
                $logData = getLogEntries($filePath, $page, $perPage, $search);
                echo json_encode(['success' => true, 'data' => $logData]);
            } else {
                echo json_encode(['success' => false, 'message' => "Error: Log file '$logFile' not found."]);
            }
        } else {
            echo json_encode(['success' => false, 'message' => "Error: No log file specified."]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'message' => "Error: Unknown action '$action'."]);
        break;
}

/**
 * Sanitize file name to prevent directory traversal attacks
 * 
 * @param string $fileName File name to sanitize
 * @return string Sanitized file name
 */
function sanitizeFileName($fileName) {
    // Remove any directory traversal attempts
    $fileName = str_replace(['../', '..\\', '/', '\\'], '', $fileName);
    
    // Only allow alphanumeric characters, dots, underscores, and hyphens
    $fileName = preg_replace('/[^a-zA-Z0-9._-]/', '', $fileName);
    
    return $fileName;
}

/**
 * Get log file entries with pagination
 * 
 * @param string $filePath Path to the log file
 * @param int $page Current page number
 * @param int $perPage Number of entries per page
 * @param string $search Search term (optional)
 * @return array Array containing log entries and pagination info
 */
function getLogEntries($filePath, $page = 1, $perPage = 100, $search = '') {
    // Initialize result array
    $result = [
        'entries' => [],
        'total' => 0,
        'pages' => 0,
        'current_page' => $page
    ];
    
    // Check if file exists
    if (!file_exists($filePath) || !is_file($filePath)) {
        return $result;
    }
    
    // Read the file
    $content = file_get_contents($filePath);
    if (empty($content)) {
        return $result;
    }
    
    // Split content into lines
    $lines = explode("\n", $content);
    $lines = array_filter($lines); // Remove empty lines
    
    // Apply search filter if provided
    if (!empty($search)) {
        $lines = array_filter($lines, function($line) use ($search) {
            return stripos($line, $search) !== false;
        });
    }
    
    // Calculate pagination
    $result['total'] = count($lines);
    $result['pages'] = ceil($result['total'] / $perPage);
    $result['current_page'] = min(max(1, $page), max(1, $result['pages']));
    
    // Get entries for the current page
    $start = ($result['current_page'] - 1) * $perPage;
    $result['entries'] = array_slice($lines, $start, $perPage);
    
    // Parse entries
    $parsedEntries = [];
    foreach ($result['entries'] as $entry) {
        $parsedEntries[] = parseLogEntry($entry);
    }
    $result['entries'] = $parsedEntries;
    
    return $result;
}

/**
 * Parse log entry to extract timestamp, file, line, and message
 * 
 * @param string $entry Log entry
 * @return array Parsed log entry
 */
function parseLogEntry($entry) {
    $parsed = [
        'timestamp' => '',
        'file' => '',
        'line' => '',
        'message' => $entry
    ];
    
    // Try to match the tcs_log format: [timestamp] [file:line] message
    if (preg_match('/^\[(.*?)\] \[(.*?):(.*?)\] (.*)$/', $entry, $matches)) {
        $parsed['timestamp'] = $matches[1];
        $parsed['file'] = $matches[2];
        $parsed['line'] = $matches[3];
        $parsed['message'] = $matches[4];
    }
    
    return $parsed;
}
