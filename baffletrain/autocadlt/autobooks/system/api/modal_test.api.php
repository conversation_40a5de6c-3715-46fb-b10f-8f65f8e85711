<?php
namespace api\modal_test;

/**
 * Test API for modal tab functionality
 */

function test_tab_1($p) {
    echo '<div class="p-6">
        <h2 class="text-xl font-bold mb-4">Test Tab 1</h2>
        <p class="mb-4">This is the content for test tab 1.</p>
        <button type="button"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-post="' . APP_ROOT . '/api/modal_test/test_tab_2"
                hx-target="#modal_body"
                hx-swap="innerHTML"
                hx-vals="{&quot;tab_title&quot;: &quot;Test Tab 2&quot;}">
            Open Test Tab 2
        </button>
    </div>';
}

function test_tab_2($p) {
    echo '<div class="p-6">
        <h2 class="text-xl font-bold mb-4">Test Tab 2</h2>
        <p class="mb-4">This is the content for test tab 2.</p>
        <button type="button"
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-post="' . APP_ROOT . '/api/modal_test/test_tab_3"
                hx-target="#modal_body"
                hx-swap="innerHTML"
                hx-vals="{&quot;tab_title&quot;: &quot;Test Tab 3&quot;}">
            Open Test Tab 3
        </button>
    </div>';
}

function test_tab_3($p) {
    echo '<div class="p-6">
        <h2 class="text-xl font-bold mb-4">Test Tab 3</h2>
        <p class="mb-4">This is the content for test tab 3.</p>
        <p class="text-sm text-gray-600">Try pinning tabs and switching between them!</p>
    </div>';
}
