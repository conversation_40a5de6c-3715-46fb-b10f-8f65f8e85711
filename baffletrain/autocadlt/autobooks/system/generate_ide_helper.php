<?php
/**
 * IDE Helper Generator
 * 
 * This script generates an IDE helper file for dynamically created constants
 * to help IDEs recognize them and suppress false "undefined constant" errors.
 * 
 * Usage:
 *   php system/generate_ide_helper.php
 *   php system/generate_ide_helper.php --force
 * 
 * Options:
 *   --force    Force regeneration even if file exists and is recent
 *   --help     Show this help message
 */

// Check for command line arguments
$options = getopt('', ['force', 'help']);

if (isset($options['help'])) {
    echo "IDE Helper Generator\n";
    echo "===================\n\n";
    echo "Generates an IDE helper file for dynamically created constants.\n\n";
    echo "Usage:\n";
    echo "  php system/generate_ide_helper.php [options]\n\n";
    echo "Options:\n";
    echo "  --force    Force regeneration even if file exists and is recent\n";
    echo "  --help     Show this help message\n\n";
    echo "The generated file will be saved as '_ide_helper_constants.php' in the\n";
    echo "application root directory.\n\n";
    exit(0);
}

// Set up the environment
$path = [];
$path['fs_app_root'] = str_replace("system", "", __DIR__);

// Include the paths file to get the functions
include($path['fs_app_root'] . DIRECTORY_SEPARATOR . "system/paths.php");

// Load the path schema
$schema_file = $path['fs_app_root'] . DIRECTORY_SEPARATOR . 'system/config/path_schema.php';
$schema = file_exists($schema_file) ? include($schema_file) : [];

/**
 * Generate IDE helper file for constants
 *
 * @param array $constants_defined Array of constants with metadata
 * @return void
 */
function generate_ide_helper_constants($constants_defined): void {
    $app_root = $constants_defined['FS_APP_ROOT']['value'] ?? dirname(__DIR__) . '/';
    $helper_file = $app_root . '_ide_helper_constants.php';

    // Load path definitions
    $definitions_file = $app_root . 'system/config/path_definitions.php';
    $definitions = file_exists($definitions_file) ? include($definitions_file) : [];

    // Load existing constants from schema to get descriptions
    $schema_file = $app_root . 'system/config/path_schema.php';
    $schema = file_exists($schema_file) ? include($schema_file) : [];

    // Build file header using definitions
    $header = $definitions['ide_helper']['file_header'] ?? [];
    $safety = $definitions['ide_helper']['safety_check'] ?? [];

    $content = "<?php\n";
    $content .= "/**\n";
    $content .= " * " . ($header['title'] ?? 'IDE Helper for Dynamically Generated Constants') . "\n";
    $content .= " * \n";
    $content .= " * " . ($header['description'] ?? 'This file is auto-generated to help IDEs recognize dynamically created constants.') . "\n";
    $content .= " * Generated on: " . date('Y-m-d H:i:s') . "\n";
    $content .= " * \n";
    $content .= " * " . ($header['warning'] ?? 'DO NOT EDIT THIS FILE MANUALLY - it will be overwritten.') . "\n";
    $content .= " * " . ($header['regenerate_command'] ?? 'To regenerate: php system/generate_ide_helper.php') . "\n";
    $content .= " */\n\n";

    $content .= "// Prevent execution if included directly\n";
    $condition = $safety['condition'] ?? "!defined('FS_APP_ROOT')";
    $message = $safety['message'] ?? 'This file should not be executed directly.';
    $content .= "if ($condition) {\n";
    $content .= "    die('$message');\n";
    $content .= "}\n\n";

    // Group constants by category for better organization
    $categories = $definitions['categories'] ?? [
        'FS_' => 'File System Paths',
        'APP_' => 'Application Paths',
        'OTHER' => 'Other Constants'
    ];

    $categorized_constants = [];
    $other_constants = [];

    foreach ($constants_defined as $name => $info) {
        $categorized = false;
        foreach ($categories as $prefix => $category) {
            if (str_starts_with($name, $prefix)) {
                $categorized_constants[$category][$name] = $info;
                $categorized = true;
                break;
            }
        }
        if (!$categorized) {
            $other_constants[$name] = $info;
        }
    }

    // Generate constants by category
    $separator = $definitions['ide_helper']['section_separator'] ?? str_repeat('=', 60);

    foreach ($categorized_constants as $category => $constants) {
        $content .= "// $separator\n";
        $content .= "// $category\n";
        $content .= "// $separator\n\n";

        foreach ($constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema, $definitions);
        }
        $content .= "\n";
    }

    // Generate other constants
    if (!empty($other_constants)) {
        $content .= "// $separator\n";
        $content .= "// Other Constants\n";
        $content .= "// $separator\n\n";

        foreach ($other_constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema, $definitions);
        }
    }

    // Write the file
    file_put_contents($helper_file, $content);
}

/**
 * Generate a single constant definition with documentation
 *
 * @param string $name Constant name
 * @param array $info Constant information
 * @param array $schema Path schema for descriptions
 * @param array $definitions Path definitions configuration
 * @return string
 */
function generate_constant_definition($name, $info, $schema, $definitions = []): string {
    $value = $info['value'];
    $type = $info['type'];
    $source_key = $info['source_key'];

    // Generate description based on constant name and schema
    $description = generate_constant_description($name, $source_key, $schema, $definitions);

    $content = "/**\n";
    $content .= " * $description\n";
    $content .= " * \n";
    $content .= " * @var $type\n";
    $content .= " * @source $source_key\n";
    $content .= " */\n";

    // Format the value for the define statement
    if (is_string($value)) {
        $formatted_value = "'" . addslashes($value) . "'";
    } elseif (is_bool($value)) {
        $formatted_value = $value ? 'true' : 'false';
    } elseif (is_null($value)) {
        $formatted_value = 'null';
    } else {
        $formatted_value = var_export($value, true);
    }

    $content .= "if (!defined('$name')) define('$name', $formatted_value);\n\n";

    return $content;
}

/**
 * Generate description for a constant based on its name and source
 *
 * @param string $name Constant name
 * @param string $source_key Source key from path array
 * @param array $schema Path schema
 * @param array $definitions Path definitions configuration
 * @return string
 */
function generate_constant_description($name, $source_key, $schema, $definitions = []): string {
    // Load descriptions from definitions file
    $descriptions = $definitions['descriptions'] ?? [];

    // Check for specific description first
    if (isset($descriptions[$name])) {
        return $descriptions[$name];
    }

    // Generate description based on patterns from definitions
    $patterns = $definitions['patterns'] ?? [];

    foreach ($patterns as $prefix => $template) {
        if (str_starts_with($name, $prefix)) {
            $part = strtolower(substr($name, strlen($prefix)));
            return str_replace('{part}', $part, $template);
        }
    }

    // Default description
    $default_template = $definitions['default_description'] ?? 'Dynamically generated constant from {source_key}';
    return str_replace('{source_key}', $source_key, $default_template);
}

echo "IDE Helper Generator\n";
echo "===================\n\n";

// Check if we should regenerate
$helper_file = $path['fs_app_root'] . DIRECTORY_SEPARATOR . '_ide_helper_constants.php';
$force = isset($options['force']);

if (!$force && file_exists($helper_file)) {
    $file_age = time() - filemtime($helper_file);
    if ($file_age < 3600) { // Less than 1 hour old
        echo "✓ IDE helper file exists and is recent (generated " . 
             round($file_age / 60) . " minutes ago)\n";
        echo "  Use --force to regenerate anyway\n\n";
        exit(0);
    }
}

echo "Generating IDE helper file...\n";

// Build the paths to get all constants
$path = build_paths($path, $schema);

// Simulate the constants that would be created
$constants_defined = [];
foreach ($path as $key => $value) {
    $constant_name = strtoupper($key);
    if (is_string($value)) {
        // Apply the same normalization as build_constants
        $is_absolute = str_starts_with($value, '/');
        $has_trailing_slash = str_ends_with($value, '/');
        $normalized = normalize_path($value);
        $value = ($is_absolute ? '/' : '') . $normalized . ($has_trailing_slash ? '/' : '');
    }
    
    $constants_defined[$constant_name] = [
        'value' => $value,
        'type' => gettype($value),
        'source_key' => $key
    ];
}

// Load additional constants from definitions
$definitions_file = $path['fs_app_root'] . DIRECTORY_SEPARATOR . 'system/config/path_definitions.php';
$definitions = file_exists($definitions_file) ? include($definitions_file) : [];
$additional_constants = $definitions['additional_constants'] ?? [];

// Convert additional constants to the expected format and add descriptions
$formatted_additional = [];
foreach ($additional_constants as $name => $config) {
    $formatted_additional[$name] = [
        'value' => $config['value'],
        'type' => $config['type'],
        'source_key' => $config['source_key']
    ];

    // Add the description to the definitions for this constant
    if (isset($config['description'])) {
        $definitions['descriptions'][$name] = $config['description'];
    }
}

$constants_defined = array_merge($constants_defined, $formatted_additional);

// Generate the IDE helper file
generate_ide_helper_constants($constants_defined);

echo "✓ IDE helper file generated: " . basename($helper_file) . "\n";
echo "✓ Total constants documented: " . count($constants_defined) . "\n\n";

echo "To use this in your IDE:\n";
echo "1. The file is automatically included when constants are built\n";
echo "2. Your IDE should now recognize all dynamically generated constants\n";
echo "3. Re-run this script when you add new constants or paths\n\n";

echo "Note: Add '_ide_helper_constants.php' to your .gitignore if you don't\n";
echo "want to commit the generated file to version control.\n\n";
?>
