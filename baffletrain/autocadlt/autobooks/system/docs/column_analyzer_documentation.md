# Intelligent Column Analyzer System

## Overview

The Intelligent Column Analyzer System enhances the autobooks data table generator with smart column name resolution. It analyzes ambiguous column names (like 'name', 'title', 'description') and suggests more descriptive alternatives based on multi-factor analysis.

## Features

- **Multi-factor Analysis**: Combines column name patterns, data content analysis, and contextual information
- **Configurable Rules**: Easily maintainable configuration files for patterns and mappings
- **Integration**: Seamlessly integrates with existing data table generator
- **Demo Interface**: Interactive web interface to test and demonstrate the system
- **Confidence Scoring**: Provides confidence levels for all suggestions

## Architecture

### Core Components

1. **`column_analyzer.class.php`** - Main analysis engine
2. **`column_mapping_rules.php`** - Pattern definitions and standard column names
3. **`data_patterns.php`** - Regex patterns and keyword lists for data analysis
4. **Enhanced `data_table_generator.class.php`** - Integrated intelligent naming
5. **Demo Interface** - Web-based demonstration and testing tools

### Analysis Framework

The system uses a weighted multi-factor approach:

- **Column Name Analysis (30%)** - Pattern matching with known column types
- **Data Pattern Analysis (50%)** - Analysis of actual data content
- **Context Analysis (20%)** - Relationship with other columns in the table

## Configuration

### Column Mapping Rules (`system/config/column_mapping_rules.php`)

Contains:
- **Standard Column Names**: Preferred naming conventions based on autobooks patterns
- **Pattern Matching Rules**: Exact, partial, and generic pattern matches
- **Context Rules**: Logic for disambiguating based on table context
- **Source Prefixes**: Fallback prefixes when confidence is low

### Data Patterns (`system/config/data_patterns.php`)

Contains:
- **Regex Patterns**: For detecting emails, business entities, software names, etc.
- **Keyword Lists**: Business terms, personal names, software keywords
- **Sampling Configuration**: How to sample data for analysis
- **Analysis Weights**: Configurable weights for different factors
- **Decision Thresholds**: Confidence levels for making suggestions

## Usage

### Automatic Integration

The system automatically enhances column labels in data tables when enabled:

```php
// Generate table with intelligent naming (default)
$config = data_table_generator::generate_table_config($table_name, $criteria, [
    'use_intelligent_naming' => true  // Default: true
]);
```

### Manual Analysis

Analyze individual columns or entire tables:

```php
// Analyze a single column
$analysis = column_analyzer::analyze_column($table_name, $column_name, $all_columns);

// Analyze all columns in a table
$table_analysis = column_analyzer::analyze_table_columns($table_name);
```

### API Endpoints

The system provides REST API endpoints for integration:

- `GET /api/system/column_analyzer_demo/get_tables` - List available tables
- `GET /api/system/column_analyzer_demo/analyze_table?table_name=X` - Analyze table
- `GET /api/system/column_analyzer_demo/analyze_column?table_name=X&column_name=Y` - Analyze column
- `GET /api/system/column_analyzer_demo/show_config` - View configuration

## Examples

### Before and After

**Original Column Names:**
- `name` → **Company Name** (confidence: 85%)
- `email` → **Email Address** (confidence: 95%)
- `status` → **Status** (confidence: 90%)

**Analysis Results:**
```php
[
    'original_name' => 'name',
    'suggested_name' => 'company_name',
    'confidence' => 85.3,
    'reasoning' => 'High confidence match for company_name based on business entity patterns in data'
]
```

### Configuration Example

```php
// Add new pattern to column_mapping_rules.php
'partial_matches' => [
    'cust_name' => ['type' => 'customer_name', 'confidence' => 75],
    'prod_title' => ['type' => 'product_name', 'confidence' => 80]
]

// Add new regex pattern to data_patterns.php
'customer_id' => [
    'pattern' => '/^CUST-[A-Z0-9]+$/i',
    'confidence' => 90,
    'min_matches' => 1,
    'description' => 'Customer ID format'
]
```

## Installation & Setup

### 1. Files Created/Modified

**New Files:**
- `system/classes/column_analyzer.class.php`
- `system/config/column_mapping_rules.php`
- `system/config/data_patterns.php`
- `resources/views/system/column_analyzer_demo.api.php`
- `resources/views/system/column_analyzer_demo.edge.php`
- `system/test_column_analyzer.php`
- `system/add_column_analyzer_navigation.php`

**Modified Files:**
- `system/classes/data_table_generator.class.php` - Added intelligent naming integration
- `system/classes/database.class.php` - Added column statistics method

### 2. Database Setup

Add navigation entry (run once):
```bash
php system/add_column_analyzer_navigation.php
```

### 3. Testing

Run the test suite:
```bash
php system/test_column_analyzer.php
```

### 4. Access Demo

Navigate to: `/system/column_analyzer_demo` (requires admin/dev role)

## Decision Logic

The system uses the following decision thresholds:

- **≥70% confidence**: Use suggested name
- **50-70% confidence**: Use highest scoring option
- **40-50% confidence**: Add context prefix (e.g., `customer_name`)
- **<40% confidence**: Keep original name

## Performance Considerations

- **Data Sampling**: Analyzes sample of records (default: 20) for performance
- **Caching**: Analysis results can be cached for repeated use
- **Lazy Loading**: Analysis only performed when intelligent naming is enabled
- **Error Handling**: Graceful fallback to standard formatting on errors

## Extending the System

### Adding New Patterns

1. **Column Name Patterns**: Add to `column_mapping_rules.php`
2. **Data Patterns**: Add regex or keywords to `data_patterns.php`
3. **Context Rules**: Define new disambiguation logic
4. **Standard Names**: Add new preferred column names

### Custom Analysis

Extend the `column_analyzer` class for custom analysis logic:

```php
class custom_column_analyzer extends column_analyzer {
    protected static function analyze_custom_patterns($data) {
        // Custom analysis logic
    }
}
```

## Troubleshooting

### Common Issues

1. **No suggestions generated**: Check if table has data and patterns match
2. **Low confidence scores**: Review and adjust pattern definitions
3. **Performance issues**: Reduce sample size in configuration
4. **API errors**: Verify database connectivity and table existence

### Debug Mode

Enable detailed logging:
```php
// In column_analyzer.class.php
private static $debug_mode = true;
```

### Log Files

Check logs for analysis details:
- `logs/column_analyzer.log` - Analysis results and errors
- `logs/router_errors.log` - Routing issues

## Future Enhancements

- **Machine Learning**: Train models on existing column patterns
- **User Feedback**: Learn from user corrections and preferences
- **Batch Processing**: Analyze multiple tables simultaneously
- **Export/Import**: Share configurations between environments
- **Performance Metrics**: Track analysis accuracy and performance

## Support

For issues or questions:
1. Check the test suite output for diagnostic information
2. Review log files for detailed error messages
3. Use the demo interface to test specific scenarios
4. Consult the configuration files for pattern examples
