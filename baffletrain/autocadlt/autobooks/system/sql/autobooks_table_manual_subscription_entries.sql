-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: manual_subscription_entries
-- Records: 3

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `manual_subscription_entries`
--

DROP TABLE IF EXISTS `manual_subscription_entries`;
CREATE TABLE `manual_subscription_entries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `admin_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subscription_reference` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subscription_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_1` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line_2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state_province` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_company_name` (`company_name`),
  KEY `idx_email_address` (`email_address`),
  KEY `idx_contact_email` (`contact_email`),
  KEY `idx_admin_email` (`admin_email`),
  KEY `idx_subscription_reference` (`subscription_reference`),
  KEY `idx_subscription_number` (`subscription_number`),
  KEY `idx_reference_number` (`reference_number`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_end_date` (`end_date`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `manual_subscription_entries`
--

INSERT INTO `manual_subscription_entries` (`id`, `company_name`, `email_address`, `contact_email`, `admin_email`, `contact_name`, `phone_number`, `subscription_reference`, `subscription_number`, `reference_number`, `product_name`, `quantity`, `start_date`, `end_date`, `status`, `notes`, `address_line_1`, `address_line_2`, `city`, `state_province`, `postal_code`, `country`, `created_at`, `updated_at`, `created_by`, `updated_by`) VALUES
('1', 'Test Company Ltd', '<EMAIL>', NULL, NULL, 'John Smith', NULL, 'TEST-12345-67890', NULL, NULL, 'AutoCAD LT', '5', '2024-01-01', '2025-01-01', 'Active', 'Manually entered test subscription', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-07 15:14:57', '2025-07-07 15:14:57', NULL, NULL),
('2', 'Sample Corp', '<EMAIL>', NULL, NULL, 'Jane Doe', NULL, 'SAMPLE-98765-43210', NULL, NULL, 'AutoCAD', '10', '2024-02-01', '2025-02-01', 'Active', 'Another test entry', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-07 15:14:57', '2025-07-07 15:14:57', NULL, NULL),
('3', 'Demo Industries', '<EMAIL>', NULL, NULL, 'Bob Johnson', NULL, 'DEMO-11111-22222', NULL, NULL, 'Inventor Professional', '3', '2024-03-01', '2025-03-01', 'Expiring Soon', 'Demo subscription entry', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-07 15:14:57', '2025-07-07 15:14:57', NULL, NULL);

COMMIT;
