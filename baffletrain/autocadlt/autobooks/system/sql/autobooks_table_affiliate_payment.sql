-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: affiliate_payment
-- Records: 0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `affiliate_payment`
--

DROP TABLE IF EXISTS `affiliate_payment`;
CREATE TABLE `affiliate_payment` (
  `affiliate_payment_id` int(11) NOT NULL AUTO_INCREMENT,
  `affiliate_id` int(11) NOT NULL DEFAULT 0,
  `affiliate_payment` decimal(15,2) NOT NULL DEFAULT 0.00,
  `affiliate_payment_tax` decimal(15,2) NOT NULL DEFAULT 0.00,
  `affiliate_payment_total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `affiliate_payment_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `affiliate_payment_last_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `affiliate_payment_status` int(5) NOT NULL DEFAULT 0,
  `affiliate_firstname` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_lastname` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_street_address` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_suburb` varchar(64) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_city` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_postcode` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_country` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `affiliate_company` varchar(60) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  `affiliate_state` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  `affiliate_address_format_id` int(5) NOT NULL DEFAULT 0,
  `affiliate_last_modified` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`affiliate_payment_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

COMMIT;
