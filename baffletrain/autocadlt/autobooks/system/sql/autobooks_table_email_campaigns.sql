-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: email_campaigns
-- Records: 3

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `email_campaigns`
--

DROP TABLE IF EXISTS `email_campaigns`;
CREATE TABLE `email_campaigns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'Campaign name/title',
  `description` text DEFAULT NULL COMMENT 'Campaign description',
  `type` enum('subscription_renewal','general','promotional','notification') NOT NULL DEFAULT 'general',
  `status` enum('draft','active','paused','completed','archived') NOT NULL DEFAULT 'draft',
  `template_id` int(11) DEFAULT NULL COMMENT 'Reference to email_campaign_templates',
  `target_audience` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON criteria for target selection',
  `send_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON rules for when to send',
  `send_schedule` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'JSON schedule configuration',
  `from_email` varchar(255) DEFAULT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `reply_to` varchar(255) DEFAULT NULL,
  `subject_template` varchar(500) DEFAULT NULL,
  `email_template` varchar(255) DEFAULT NULL COMMENT 'Template file name',
  `data_source_id` int(11) DEFAULT NULL COMMENT 'Reference to autobooks_data_sources',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_run_at` timestamp NULL DEFAULT NULL,
  `next_run_at` timestamp NULL DEFAULT NULL,
  `total_sent` int(11) DEFAULT 0,
  `total_delivered` int(11) DEFAULT 0,
  `total_failed` int(11) DEFAULT 0,
  `is_system` tinyint(1) DEFAULT 0 COMMENT 'System-generated campaign (like existing subscription renewal)',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_next_run` (`next_run_at`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_data_source_id` (`data_source_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='Email campaign definitions and settings';

-- Dumping data for table `email_campaigns`
--

INSERT INTO `email_campaigns` (`id`, `name`, `description`, `type`, `status`, `template_id`, `target_audience`, `send_rules`, `send_schedule`, `from_email`, `from_name`, `reply_to`, `subject_template`, `email_template`, `data_source_id`, `created_by`, `created_at`, `updated_at`, `last_run_at`, `next_run_at`, `total_sent`, `total_delivered`, `total_failed`, `is_system`) VALUES
('1', 'Autodesk Subscription Renewal Reminder', 'Automated reminders for Autodesk subscription renewals based on expiration date', 'subscription_renewal', 'paused', NULL, NULL, '{\"days_before_expiry\":[\"90\",\"60\",\"30\",\"15\"]}', '{\"send_days\":[0,1,1,1,1,1,0],\"send_time\":11}', '<EMAIL>', 'TCS CAD & BIM Solutions Limited', NULL, 'Your {{subs_product_name}} subscription {{subs_status,', 'autodesk/reminder_email.emlt.php', '1', '1', '2025-07-20 00:56:12', '2025-07-28 12:57:43', NULL, NULL, '800', '0', '0', '1'),
('2', 'Autodesk Auto Renewals', '', 'general', 'active', NULL, NULL, NULL, NULL, '<EMAIL>', 'TCS CAD & BIM Solutions Limited', NULL, 'Your Autodesk Subscriptions', 'auto_renew.emlt.php', '1', '2', '2025-07-24 10:27:04', '2025-07-29 14:01:20', '2025-07-29 13:01:20', NULL, '722', '0', '0', ''),
('3', 'test', '', 'general', 'active', NULL, NULL, NULL, NULL, '<EMAIL>', 'TCS CAD & BIM Solutions Limited', NULL, 'Test Email for {{name}} - {{email}}', 'test.emlt.php', '2', '2', '2025-07-28 13:43:24', '2025-07-28 16:53:32', '2025-07-28 15:53:32', NULL, '41', '0', '0', '');

COMMIT;
