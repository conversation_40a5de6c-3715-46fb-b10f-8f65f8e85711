-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- Generated by Autobooks Database Dump Manager
-- Generation Time: Aug 15, 2025 at 07:45 AM
-- Table: affiliate_payment_status
-- Records: 6

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

-- Table structure for table `affiliate_payment_status`
--

DROP TABLE IF EXISTS `affiliate_payment_status`;
CREATE TABLE `affiliate_payment_status` (
  `affiliate_payment_status_id` int(11) NOT NULL DEFAULT 0,
  `affiliate_language_id` int(11) NOT NULL DEFAULT 1,
  `affiliate_payment_status_name` varchar(32) COLLATE utf8_unicode_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`affiliate_payment_status_id`,`affiliate_language_id`),
  KEY `idx_affiliate_payment_status_name` (`affiliate_payment_status_name`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- Dumping data for table `affiliate_payment_status`
--

INSERT INTO `affiliate_payment_status` (`affiliate_payment_status_id`, `affiliate_language_id`, `affiliate_payment_status_name`) VALUES
('0', '1', 'Pending'),
('0', '2', 'Offen'),
('0', '3', 'Pendiente'),
('1', '1', 'Paid'),
('1', '2', 'Ausgezahlt'),
('1', '3', 'Pagado');

COMMIT;
