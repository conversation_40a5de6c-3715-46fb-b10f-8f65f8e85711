@props([
'title' => 'Select',
'description' => 'Basic form select',
'data' => [], // An array of items.
'label' => '',
'name' => '',
'class_suffix' => '',
'options' => [], // An array of options: ['label' => 'Name', 'value' => 'name'],
'selected' => null, // The selected option ['label' => 'Name', 'value' => 'name'],
'multiple' => 0
])

<div>
  <label for="{{ $id ?? 'select' }}" class="block text-sm/6 font-medium text-gray-900">
    {{ $label ?? 'Select an option' }}
  </label>
  <div class="grid grid-cols-1">
    <select
            id="{{ $id ?? 'select' }}"
            name="{{ $name ?? 'select' }}"
            {{ $extra_attributes }}
            class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 {{ $class_suffix }}" {{ $multiple ? 'multiple' : '' }}>
    @foreach($options ?? [] as $key => $value)
      <option value="{{ $key }}" {{ ($selected ?? null) == $key ? 'selected' : '' }}>
        {{ $value }}
      </option>
      @endforeach
      </select>
      @if ($multiple!=1)
      <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
      @endif
  </div>
</div>
{{--
<div>
  <label for="location" class="block text-sm/6 font-medium text-gray-900">Location</label>
  <div class="mt-2 grid grid-cols-1">
    <select id="location" name="location" class="col-start-1 row-start-1 w-full appearance-none rounded-md bg-white py-1.5 pl-3 pr-8 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6">
      <option>United States</option>
      <option selected>Canada</option>
      <option>Mexico</option>
    </select>
    <svg class="pointer-events-none col-start-1 row-start-1 mr-2 size-5 self-center justify-self-end text-gray-500 sm:size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
      <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
    </svg>
  </div>
</div>
--}}