@props([
  'title' => 'Select',
  'description' => 'form select with select and insert button',
  'selected' => null, // The selected option ['label' => 'Name', 'value' => 'name'],
  'name' => '',
  'input' => [
        'label' => '',
        'name' => '',
        'value' => '',
        'class_suffix' => '',
        'name_suffix' => '',
  ],
  'select' => [
        'name' => '',
        'class_suffix' => '',
        'name_suffix' => '',
        'options' => [], // An array of options: ['label' => 'Name', 'value' => 'name'],
        'selected' => null, // The selected option ['label' => 'Name', 'value' => 'name'],
  ],
  'button' => [
        'class_suffix' => ''
  ],
])
@php
//set up defaults
if ($select['name_suffix'] == '') $select['name_suffix'] = "{$name}_select";
if ($input['name_suffix'] == '') $input['name_suffix'] = "{$name}_input";
@endphp
<div>
  @if ($label)
    <label for="email" class="block text-sm/6 font-medium text-gray-900">{{ $label }}</label>
  @endif
  <div class="mt-2">
    <div class="flex items-center rounded-md bg-white pl-3 outline outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline has-[input:focus-within]:outline-2 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-600">
      <input type="{{ $type ?? 'text' }}"
             name="{{ $name }}{{ $input['name_suffix'] }}"
             id="{{ $id }}"
             class="block min-w-0 grow py-1.5 pl-1 pr-3 text-base text-gray-900 placeholder:text-gray-400 focus:outline focus:outline-0 sm:text-sm/6" placeholder="">
      <button type="button" class="flex shrink-0 items-center gap-x-1.5 rounded-r-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 hover:bg-gray-50 focus:relative focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">
        <svg class="-ml-0.5 size-4 text-gray-400" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
          <path fill-rule="evenodd" d="M2 2.75A.75.75 0 0 1 2.75 2h9.5a.75.75 0 0 1 0 1.5h-9.5A.75.75 0 0 1 2 2.75ZM2 6.25a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 0 1.5h-5.5A.75.75 0 0 1 2 6.25Zm0 3.5A.75.75 0 0 1 2.75 9h3.5a.75.75 0 0 1 0 1.5h-3.5A.75.75 0 0 1 2 9.75ZM9.22 9.53a.75.75 0 0 1 0-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v5.69a.75.75 0 0 1-1.5 0V8.56l-.97.97a.75.75 0 0 1-1.06 0Z" clip-rule="evenodd" />
        </svg>
        Sort
      </button>
      <div class="grid shrink-0 grid-cols-1 focus-within:relative">
        <x-forms-select-invisible
            id="{{ $id ?? 'select' }}"
            name="{{ $name }}{{ $select['name_suffix'] }}"
            class="col-start-1 row-start-1 w-full appearance-none rounded-md py-1.5 pl-3 pr-7 text-base text-gray-500 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
            :options="$options"
        />
      </div>

    </div>
  </div>
</div>
