@props([
    'name' => 'template_data',
    'filetype' => '.csv,.txt',
    'uploadLocation' => 'uploads/csv/',
    'required' => false,
    'title' => 'Database Table Template',
    'description' => 'This template creates a database table and imports CSV data automatically.',
    'color' => 'blue'
])

<div class="space-y-3">
    <div class="bg-{{ $color }}-50 border border-{{ $color }}-200 rounded-lg p-4">
        <h4 class="text-sm font-medium text-{{ $color }}-800 mb-2">{{ $title }}</h4>
        <p class="text-sm text-{{ $color }}-700 mb-3">{{ $description }}</p>

        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">CSV Data (Optional)</label>
            <textarea
                name="{{ $name }}[csv_data]"
                rows="4"
                placeholder="Paste CSV data here (optional - you can also upload data later)..."
                class="block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6"
                @if($required) required @endif
            ></textarea>
        </div>
        
        <div class="mt-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">Or Upload CSV File</label>
            <input
                type="file"
                name="csv_file"
                accept="{{ $filetype }}"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                @if($required) required @endif
            />
        </div>
        
        <p class="text-xs text-{{ $color }}-600 mt-2">💡 You can manage data later through the settings page</p>
    </div>
</div>
