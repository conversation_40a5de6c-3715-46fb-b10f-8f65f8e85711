<?php
include('includes/application_top.php');

// CSRF Protection for batch upload
require_once('includes/functions/csrf_middleware.php');
if (!tep_csrf_check_token('admin_batch_upload')) {
    header('Content-Type: application/json');
    http_response_code(403);
    echo json_encode(['error' => 'Security validation failed']);
    exit;
}

$products_id = (int)$_POST['products_id'];
$pi_sort_order = 0;
$piArray = [];

if (!empty($_FILES['batchUpload']['name'][0])) {
    foreach ($_FILES['batchUpload']['name'] as $key => $name) {
        $pi_sort_order++;
        $tmp_name = $_FILES['batchUpload']['tmp_name'][$key];
        $new_name = 'image_' . time() . '_' . $name;
        $destination = DIR_FS_CATALOG_IMAGES . $new_name;

        if (move_uploaded_file($tmp_name, $destination)) {
            $sql_data_array = [
                'products_id' => $products_id,
                'image' => $new_name,
                'htmlcontent' => '',
                'sort_order' => $pi_sort_order
            ];
            tep_db_perform(TABLE_PRODUCTS_IMAGES, $sql_data_array);
            $image_id = tep_db_insert_id();
            $piArray[] = [
                'id' => $image_id,
                'url' => DIR_WS_CATALOG_IMAGES . $new_name
            ];
        }
    }
}

echo json_encode($piArray);
?>
