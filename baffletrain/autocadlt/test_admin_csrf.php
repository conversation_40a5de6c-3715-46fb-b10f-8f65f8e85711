<?php
/*
  Admin CSRF Protection Test Page
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2024 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');
require('includes/functions/csrf_middleware.php');

$action = (isset($_GET['action']) ? $_GET['action'] : '');

// Handle form submissions for testing
if (@tep_not_null($action)) {
    switch ($action) {
        case 'test_secure_form':
            if (tep_csrf_check_token('admin_test_secure')) {
                $messageStack->add('✅ Secure form validation PASSED!', 'success');
            } else {
                $messageStack->add('❌ Secure form validation FAILED!', 'error');
            }
            break;
            
        case 'test_auto_form':
            if (tep_csrf_check_token('admin_test_auto')) {
                $messageStack->add('✅ Auto-detected form validation PASSED!', 'success');
            } else {
                $messageStack->add('❌ Auto-detected form validation FAILED!', 'error');
            }
            break;
            
        case 'test_ajax':
            tep_csrf_validate_ajax('admin_test_ajax');
            header('Content-Type: application/json');
            echo json_encode(array(
                'success' => true,
                'message' => '✅ AJAX CSRF validation PASSED!',
                'timestamp' => date('Y-m-d H:i:s')
            ));
            exit;
            
        case 'test_api':
            $input_params = array_merge($_GET, $_POST);
            if (tep_csrf_validate_api($input_params, 'admin_test_api')) {
                header('Content-Type: application/json');
                echo json_encode(array(
                    'success' => true,
                    'message' => '✅ API CSRF validation PASSED!',
                    'timestamp' => date('Y-m-d H:i:s')
                ));
            } else {
                header('Content-Type: application/json');
                http_response_code(403);
                echo json_encode(array(
                    'error' => true,
                    'message' => '❌ API CSRF validation FAILED!'
                ));
            }
            exit;
    }
    
    tep_redirect(tep_href_link('test_admin_csrf.php'));
}

require('includes/template_top.php');
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">🔒 Admin CSRF Protection Test Suite</h3>
                </div>
                <div class="card-body">
                    
                    <?php
                    if ($messageStack->size() > 0) {
                        echo '<div class="alert-container mb-4">';
                        echo $messageStack->output();
                        echo '</div>';
                    }
                    ?>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h4>📋 Form Tests</h4>
                            
                            <!-- Test 1: Secure Form -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 1: Secure Form (Always Protected)</h5>
                                </div>
                                <div class="card-body">
                                    <?php echo tep_draw_secure_form('test_secure', 'test_admin_csrf.php', 'action=test_secure_form', 'post', '', 'admin_test_secure'); ?>
                                        <div class="form-group">
                                            <label for="test_data">Test Data:</label>
                                            <input type="text" class="form-control" name="test_data" value="Secure form test" />
                                        </div>
                                        <button type="submit" class="btn btn-success">Submit Secure Form</button>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Test 2: Auto-Detected Form -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 2: Smart Form (Auto-Detected)</h5>
                                </div>
                                <div class="card-body">
                                    <?php echo tep_draw_smart_form('test_auto', 'test_admin_csrf.php', 'action=test_auto_form', 'post', '', 'admin_test_auto'); ?>
                                        <div class="form-group">
                                            <label for="test_data2">Test Data:</label>
                                            <input type="text" class="form-control" name="test_data2" value="Auto-detected form test" />
                                        </div>
                                        <button type="submit" class="btn btn-info">Submit Auto Form</button>
                                    </form>
                                </div>
                            </div>
                            
                            <!-- Test 3: Search Form (Should NOT be protected) -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 3: Search Form (Should NOT be protected)</h5>
                                </div>
                                <div class="card-body">
                                    <?php echo tep_draw_form('search', 'test_admin_csrf.php', '', 'get'); ?>
                                        <div class="form-group">
                                            <label for="search_term">Search Term:</label>
                                            <input type="text" class="form-control" name="search_term" value="test search" />
                                        </div>
                                        <button type="submit" class="btn btn-secondary">Search (GET)</button>
                                    </form>
                                    <small class="text-muted">This form uses GET method and should not have CSRF tokens.</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h4>🔗 AJAX & API Tests</h4>
                            
                            <!-- Test 4: AJAX Test -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 4: AJAX Request</h5>
                                </div>
                                <div class="card-body">
                                    <button id="test-ajax" class="btn btn-warning">Test AJAX CSRF</button>
                                    <div id="ajax-result" class="mt-2"></div>
                                </div>
                            </div>
                            
                            <!-- Test 5: API Test -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 5: API Request</h5>
                                </div>
                                <div class="card-body">
                                    <button id="test-api" class="btn btn-danger">Test API CSRF</button>
                                    <div id="api-result" class="mt-2"></div>
                                </div>
                            </div>
                            
                            <!-- Test 6: Manual Token Test -->
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5>Test 6: Manual Token Information</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Current CSRF Token:</strong></p>
                                    <code><?php echo tep_csrf_token('admin_manual_test'); ?></code>
                                    
                                    <p class="mt-3"><strong>CSRF Protection Status:</strong></p>
                                    <span class="badge badge-<?php echo tep_csrf_protection_enabled() ? 'success' : 'danger'; ?>">
                                        <?php echo tep_csrf_protection_enabled() ? 'ENABLED' : 'DISABLED'; ?>
                                    </span>
                                    
                                    <p class="mt-3"><strong>Session Token (Legacy):</strong></p>
                                    <code><?php echo isset($sessiontoken) ? $sessiontoken : 'Not set'; ?></code>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h5>🔍 How to Test:</h5>
                                <ol>
                                    <li><strong>Valid Tests:</strong> Submit the forms above - they should work normally</li>
                                    <li><strong>Invalid Tests:</strong> Use browser dev tools to remove CSRF tokens and resubmit - should fail</li>
                                    <li><strong>AJAX Tests:</strong> Click the AJAX/API buttons - should work with automatic token inclusion</li>
                                    <li><strong>Manual Tests:</strong> Try submitting forms with expired or invalid tokens</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// AJAX Test
document.getElementById('test-ajax').addEventListener('click', function() {
    var resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Testing...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'test_admin_csrf.php?action=test_ajax', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    resultDiv.innerHTML = '<div class="alert alert-success">' + response.message + '<br><small>Time: ' + response.timestamp + '</small></div>';
                } catch (e) {
                    resultDiv.innerHTML = '<div class="alert alert-danger">Invalid response format</div>';
                }
            } else {
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ AJAX request failed (Status: ' + xhr.status + ')</div>';
            }
        }
    };
    
    xhr.send('test_data=ajax_test');
});

// API Test
document.getElementById('test-api').addEventListener('click', function() {
    var resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> Testing...';
    
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'test_admin_csrf.php?action=test_api', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            try {
                var response = JSON.parse(xhr.responseText);
                if (xhr.status === 200) {
                    resultDiv.innerHTML = '<div class="alert alert-success">' + response.message + '<br><small>Time: ' + response.timestamp + '</small></div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-danger">' + response.message + '</div>';
                }
            } catch (e) {
                resultDiv.innerHTML = '<div class="alert alert-danger">❌ API request failed (Status: ' + xhr.status + ')</div>';
            }
        }
    };
    
    xhr.send('test_data=api_test');
});
</script>

<?php require('includes/template_bottom.php'); ?>
