# Admin CSRF Protection Implementation Guide

## 🔒 Overview

This guide covers the comprehensive CSRF (Cross-Site Request Forgery) protection system implemented for the osCommerce admin area. The system provides robust security while maintaining backward compatibility with existing code.

## ✅ What's Been Implemented

### 1. Core Security Functions
- **File**: `includes/functions/security.php`
- **Purpose**: Core CSRF token generation, validation, and management
- **Features**:
  - Cryptographically secure token generation
  - Action-specific tokens with expiration
  - Session-bound validation
  - Automatic cleanup of expired tokens

### 2. Validation Middleware
- **File**: `includes/functions/csrf_middleware.php`
- **Purpose**: Easy-to-use validation functions for form processors
- **Features**:
  - Simple include-and-validate functions
  - Support for forms, AJAX, and API endpoints
  - Backward compatibility with existing sessiontoken

### 3. Enhanced Form Generation
- **File**: `includes/functions/html_output.php`
- **Enhanced Function**: `tep_draw_form()`
- **New Parameters**:
  - `$tokenize` - Controls CSRF token inclusion ('auto', true, false)
  - `$csrf_action` - Specific action name for token validation
- **New Functions**:
  - `tep_draw_secure_form()` - Always includes CSRF tokens
  - `tep_draw_smart_form()` - Auto-detects when tokens are needed
  - `tep_admin_should_secure_form()` - Smart detection logic

### 4. JavaScript CSRF Manager
- **File**: `includes/admin_csrf.js`
- **Purpose**: Automatic CSRF token handling for AJAX requests
- **Features**:
  - Auto-detection and inclusion of CSRF tokens
  - jQuery and native XMLHttpRequest support
  - HTMX integration for dynamic content
  - Error handling for failed validations

### 5. Updated Form Processors
Enhanced the following admin files with CSRF protection:
- `login.php` - Admin login and account creation
- `categories.php` - Category and product management
- `upload_batch.php` - Batch file uploads
- `api.php` - API endpoints with data modification

### 6. Template Integration
- **File**: `includes/template_top.php`
- **Added**: CSRF meta tags and JavaScript inclusion
- **Purpose**: Provides tokens for JavaScript access

### 7. Configuration
- **File**: `includes/configure.php`
- **Setting**: `CSRF_PROTECTION` - Enable/disable protection
- **Default**: Enabled ('True')

## 🚀 How to Use

### For New Forms

#### Secure Form (Always Protected)
```php
echo tep_draw_secure_form('myform', 'process.php', 'action=save', 'post', '', 'admin_save_action');
```

#### Smart Form (Auto-Detection)
```php
echo tep_draw_smart_form('myform', 'process.php', 'action=save', 'post');
```

#### Manual Control
```php
// Force protection
echo tep_draw_form('myform', 'process.php', 'action=save', 'post', '', true, 'admin_custom');

// Disable protection
echo tep_draw_form('myform', 'process.php', 'action=search', 'get', '', false);
```

### For Form Processors

#### Simple Validation
```php
require_once('includes/functions/csrf_middleware.php');
tep_csrf_require_valid_token('admin_save_action');
// Continue with form processing...
```

#### Conditional Validation
```php
require_once('includes/functions/csrf_middleware.php');
if (!tep_csrf_check_token('admin_save_action')) {
    $messageStack->add_session('Security validation failed.', 'error');
    tep_redirect(tep_href_link('admin_page.php'));
}
```

#### AJAX Endpoints
```php
require_once('includes/functions/csrf_middleware.php');
tep_csrf_validate_ajax('admin_ajax_action');
// Process AJAX request...
```

#### API Endpoints
```php
require_once('includes/functions/csrf_middleware.php');
$input_params = array_merge($_GET, $_POST);
if (!tep_csrf_validate_api($input_params, 'admin_api_action')) {
    header('Content-Type: application/json');
    http_response_code(403);
    echo json_encode(array('error' => 'Security validation failed'));
    exit;
}
```

### For JavaScript/AJAX

#### Automatic Protection
The JavaScript manager automatically protects:
- All forms with `class="csrf-protected"`
- All forms with `data-csrf="true"`
- All AJAX POST requests (jQuery and native)

#### Manual Token Access
```javascript
// Get current token
var token = AdminCSRFManager.getToken('admin_action');

// Add token to form
AdminCSRFManager.protectForm(document.getElementById('myform'), 'admin_action');
```

## 🔧 Configuration Options

### Disable CSRF Protection
```php
// In includes/configure.php
define('CSRF_PROTECTION', 'False');
```

### JavaScript Configuration
```javascript
AdminCSRFManager.init({
    debug: true,                    // Enable debug logging
    autoProtectForms: true,         // Auto-protect forms
    autoProtectAjax: true,          // Auto-protect AJAX
    tokenFieldName: 'csrf_token',   // Token field name
    metaTagName: 'csrf-token'       // Meta tag name
});
```

## 🧪 Testing

### Test Page
Visit `test_admin_csrf.php` to verify the implementation:
- Form validation tests
- AJAX request tests
- API endpoint tests
- Token information display

### Manual Testing
1. **Valid Submissions**: Forms should work normally
2. **Invalid Tokens**: Remove tokens via dev tools - should fail
3. **Expired Tokens**: Wait for expiration - should fail
4. **AJAX Requests**: Should automatically include tokens

## 🔄 Backward Compatibility

The system maintains full backward compatibility:
- Existing `sessiontoken` validation still works
- Old forms continue to function
- Gradual migration is supported
- No breaking changes to existing code

## 🛡️ Security Features

### Token Properties
- **Cryptographically Secure**: Uses `random_bytes()` or `openssl_random_pseudo_bytes()`
- **Session-Bound**: Tokens are tied to specific sessions
- **Action-Specific**: Different actions can have different tokens
- **Time-Limited**: Tokens expire after 1 hour by default
- **One-Time Use**: Tokens are consumed upon validation

### Smart Detection
The system automatically determines when forms need protection:
- **POST forms**: Protected by default
- **GET forms**: Not protected (prevents URL pollution)
- **Search forms**: Explicitly excluded
- **Manual override**: Available when needed

### Error Handling
- **Graceful Degradation**: Falls back to sessiontoken if CSRF fails
- **User-Friendly Messages**: Clear error messages for users
- **Logging**: Security violations are logged
- **Automatic Cleanup**: Expired tokens are automatically removed

## 📝 Best Practices

### For Developers
1. **Use Smart Forms**: Let the system auto-detect protection needs
2. **Specific Actions**: Use descriptive action names for tokens
3. **Test Thoroughly**: Use the test page to verify implementations
4. **Handle Errors**: Provide user-friendly error messages
5. **Log Security Events**: Monitor for potential attacks

### For Administrators
1. **Keep Protection Enabled**: Only disable for testing
2. **Monitor Logs**: Watch for CSRF validation failures
3. **Regular Updates**: Keep the security functions updated
4. **User Training**: Educate users about security messages

## 🔍 Troubleshooting

### Common Issues
1. **Forms Not Working**: Check if CSRF tokens are being generated
2. **AJAX Failures**: Verify JavaScript is loading correctly
3. **Token Mismatches**: Ensure action names match between form and processor
4. **Session Issues**: Verify session handling is working properly

### Debug Mode
Enable debug mode in JavaScript to see detailed logging:
```javascript
AdminCSRFManager.config.debug = true;
```

## 📊 Implementation Status

✅ **Complete**:
- Core security functions
- Form generation enhancements
- Key form processors updated
- JavaScript CSRF manager
- Template integration
- Configuration options
- Test page
- Documentation

🔄 **Ongoing**:
- Gradual migration of remaining forms
- Performance monitoring
- Security audit feedback integration

The admin CSRF protection system is now fully operational and ready for production use!
