# $Id$
#
# This is used with Apache WebServers
#
# For this to work, you must include the parameter 'Options' to
# the AllowOverride configuration
#
# Example:
#
# <Directory "/usr/local/apache/htdocs">
#   AllowOverride Options
# </Directory>
#
# 'All' with also work. (This configuration is in the
# apache/conf/httpd.conf file)

# The following makes adjustments to the SSL protocol for Internet
# Explorer browsers

#<IfModule mod_setenvif.c>
#  <IfDefine SSL>
#    SetEnvIf User-Agent ".*MSIE.*" \
#             nokeepalive ssl-unclean-shutdown \
#             downgrade-1.0 force-response-1.0
#  </IfDefine>
#</IfModule>

# If Search Engine Friendly URLs do not work, try enabling the
# following Apache configuration parameter

# AcceptPathInfo On

# Fix certain PHP values
# (commented out by default to prevent errors occuring on certain
# servers)

# php_value session.use_trans_sid 0
# php_value register_globals 1
<Files "*">
    Order Deny,Allow
    Deny from all
    Allow from *************
    Allow from ************
    Allow from ***********/24
    Allow from ***********/24
</Files>
Header always set X-Content-Type-Options: nosniff
Header always set Strict-Transport-Security "max-age=63072000;"
Header always set X-Xss-Protection "1; mode=block"
Header always set Content-Security-Policy "default-src 'self'; img-src 'self' data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://unpkg.com https://cdnjs.cloudflare.com https://maxcdn.bootstrapcdn.com https://fonts.googleapis.com https://code.ionicframework.com https://cdn.jsdelivr.net; font-src 'self' https://cdnjs.cloudflare.com https://maxcdn.bootstrapcdn.com https://code.ionicframework.com https://fonts.gstatic.com; child-src https://www.youtube.com;"
Header always set Referrer-Policy "strict-origin-when-cross-origin"
Header always set X-Frame-Options "ALLOW-FROM https://youtube.com/"