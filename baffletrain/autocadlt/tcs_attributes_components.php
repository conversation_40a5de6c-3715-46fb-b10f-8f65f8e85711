<?php
require_once("includes/functions/tcs_components.php");

?>



<?php

function get_attributes_tables($products_id = false):string {
   $no_id = '<strong>Save first to generate product id</strong>';
   $products_attributes = new tcs_product_attributes((int)$_GET['pID']);
   $attributes = $products_attributes->get_attributes(false);
   $attributes_table = $products_id ? tcs_draw_attributes_table($products_attributes, $products_id) : $no_id;
   $variations_table = $products_id ? tcs_draw_variations_table($products_attributes, $products_id ) : $no_id;
   print_rr($products_attributes,'products_attributes object');
   return "
        <div class=\"panel panel-default\">
            <div class=\"panel-body\">
                {$attributes_table}
                </div>
        </div>
        
        <div class=\"panel panel-default\">
            <div class=\"panel-body\">
               {$variations_table}
            </div>
        </div>";
}

function tcs_draw_attributes_table($products_attributes, $products_id):string {
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Option', 'class' => '', 'params' => ''],
        ['name' => 'Value', 'class' => '', 'params' => ''],
        ['name' => 'Default', 'class' => 'text-center', 'params' => ''],
        ['name' => 'DependsOn', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the attributes
    $attributes = $products_attributes->get_attributes(false);

    $rows = '';
    foreach ($attributes as $key_a => $attribute) {
        foreach ($attribute['values'] as $key_v => $attribute_value) {
            $rows .= tcs_draw_attributes_table_row($key_a, $key_v, $attribute, $attribute_value);
        }
    }

    $footer = tcs_draw_attributes_form_footer($products_id);

    // Output the table
    return tcs_draw_admin_bootstrap_table(
        'Attributes',
        'attributesTable',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    );
}

function tcs_draw_attributes_table_row($key_a, $key_v, $attribute, $attribute_value) {

    if (strpos($attribute_value['dependson_options_values_id'], ',') !== false) {
        $valueIds = explode(',', $attribute_value['dependson_options_values_id']);
        $dependson_options_values_name = "";
        foreach ($valueIds as $value) {
            if ($dependson_options_values_name != "") {
                $dependson_options_values_name .= ', ';
            }
            $dependson_options_values_name .= tep_values_name($value);
        }
    } else {
        $dependson_options_values_name = tep_values_name($attribute_value['dependson_options_values_id']);
    }

    $options_name = $attribute['products_options_name'];
    $values_name = $attribute_value['products_options_values_name'];
    $dependson_options_name = tep_options_name($attribute_value['dependson_options_id']);

    if (@tep_not_null($attribute_value['dependson_options_id']) && @tep_not_null($attribute_value['dependson_options_values_id'])) {
        $dependsOn_string = $dependson_options_name . ': ' . $dependson_options_values_name;
    } else {
        $dependsOn_string = "None";
    }

    $rowId = $key_a . '_' . $key_v;
    $rowClass = ($attribute_value["enabled"] == "0") ? "table-danger danger" : "";

    // Add this row's data to the dataset
    $id = "attributesTableRow_{$rowId}";

    $row_content = [
        ['class' => 'portletTD', 'id' => '', 'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '', 'id' => "attributesTable_optionsName_{$rowId}", 'content' => $options_name, 'params' => ['data-options_id' => $key_a]],
        ['class' => '', 'id' => "attributesTable_optionsValueName_{$rowId}", 'content' => $values_name, 'params' => ['data-values_id' => $key_v]],
        ['class' => 'text-center', 'id' => "attributesTable_optionsValuePriceDefault_{$rowId}", 'content' => $attribute_value["attribute_default"] ? 'Yes' : 'No'],
        ['class' => '', 'id' => "attributesTable_DependsOn_{$rowId}", 'content' => $dependsOn_string,
            'params' => [
                'data-dependson_options_id' => $attribute_value['dependson_options_id'],
                'data-dependson_values_id' => $attribute_value['dependson_options_values_id']
            ]
        ],[
            'class' => '',
            'id' => '',
            'content' => '<input class="indexSO attributesSOinput" type="text" name="" size="1" maxlength="4" value="' . $attribute_value['products_attributes_sort_order'] . '">'
        ],[
            'class' => 'attributesOptionsEdit listsOptionsEdit', 'id' => '',
            'hx-get'=>"api_h.php?action=product_attributes_product_edit",
            'hx-target'=>"#attributes_form",
            'hx-vals'=> '{"products_id": "' . (int)$_GET['pID'] . '", "products_attributes_id": "' . $attribute_value['products_attributes_id'] . '"}',
            'content' => 'e'
        ],[
            'class' => 'attributesOptionsDelete listsOptionsDelete',
            'id' => '',
            'hx-get'=>"api_h.php?action=product_attributes_product_edit",
            'hx-target' => "none",
            'hx-vals'=> '{"products_id": "' . (int)$_GET['pID'] . '", "products_attributes_id": "' . $attribute_value['products_attributes_id'] . '"}',
            'content' => 'x'
        ],
    ];
    $params = ['data-attributesid' => $attribute_value['products_attributes_id'], 'data-rowNum' => $rowId];

    // Output the table row
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content, $params);
}

function tcs_draw_attributes_form_footer($products_id, $products_attributes_id = false) {
    global $languages_id;
    $attribute = false;
    if ($products_attributes_id) $attribute = tcs_product_attributes::get_attributes_from_id($products_attributes_id);
    $options = tep_db_query("select * from products_options where language_id = '" . $languages_id . "' order by products_options_name");
   // print_rr(  $options ,'optionsz');
    ob_start();?>
    <div id="attributes_form">
        <div class="row">
            <div id="attributes_left_container" class="col-xs-6">
                <div id="attributes_options_container" class="form-inline form-group">
                    <label>Attributes: </label><br/>
                    <select id="attributes_options_id" 
                        class="form-control" 
                        name="attributes_options_id"
                        hx-get="api_h.php?action=product_attributes_getValueList" 
                        hx-target="#attributes_values_id" 
                        hx-trigger="change"
                    >
                        <option disabled="" selected="" value="">Options</option>';
                        <?php while ($option = tep_db_fetch_array($options)) {?>
                            <option name="attributes_<?= $option['products_options_name'] ?>" value="<?= $option['products_options_id'] ?>"<?= ($attribute && $attribute['options_id'] == $option['products_options_id'] ? ' selected' : '') ?>><?= $option['products_options_name'] ?></option>';
                        <?php } ?>
                    </select>
                    <div id="attributes_values_id_container" class="form-group">
                        <select id="attributes_values_id" class="form-control" data-current_id="-1">
                            <option disabled selected value>Values</option>
                            <?php
                            if ($attribute) {
                                $values = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                                        FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                                        WHERE povtpo.products_options_id = '" . (int)$attribute['options_id'] . "' 
                                                        AND povtpo.products_options_values_id = pov.products_options_values_id 
                                                        AND pov.language_id = '" . (int)$languages_id . "' 
                                                        ORDER BY pov.products_options_values_name");
                                while ($value = tep_db_fetch_array($values)) {
                                    echo '<option value="' . $value['products_options_values_id'] . '" ' . ($attribute['options_values_id'] == $value['products_options_values_id'] ? 'selected' : '') . '>' . $value['products_options_values_name'] . '</option>';
                                    //print_rr($value,'value');
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div id="attributes_values_id_btns_container" class="btn-group"></div>
                </div>

                <div id="attributes_misc_container" class="form-inline form-group">                    
                    <input type="hidden" name="attributes_price_prefix" id="attributes_price_prefix" value="+">
                    <div class="checkbox"><label><input 
                        type="checkbox"
                        name="attributes_attribute_default"
                        id="attributes_attribute_default"
                        <?= $attribute ? 'checked' : '' ?>
                        >&nbsp;Default</label></div>
                </div>
            </div>

            <div id="attributes_dependson_container" class="col-xs-5">
                <label>Depends On: </label>
                <select id="attributes_dependson_options_id"
                        name="attributes_dependson_options_id"
                        hx-vals=\'{"productsid": "' . (int)$products_id . '", "action": "product_attributes_getDependsOnValueList"}\'
                        class="form-control" 
                        hx-get="api_h.php" 
                        hx-include="this"
                        hx-target="#attributes_dependson_values_id" 
                        hx-trigger="change">
                    <option disabled selected value>Options</option>
                    <option>None</option>';
                    <?php
                       while ($options_values = tep_db_fetch_array($options)) {?>
                           <option name="attributes_dependson_<?= $options_values['products_options_name'] ?>" value="<?= $options_values['products_options_id'] ?>" <?= ($attribute && $attribute['dependson_options_id'] == $options_values['products_options_id'] ? 'selected' : '') ?>><?= $options_values['products_options_name'] ?></option>';
                    <?php } ?>
                </select>
                <select multiple id="attributes_dependson_values_id" class="form-control">
                    <option disabled selected value>Values</option>
                    <?php
                    if ($attribute) {
                        $values = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                                FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                                WHERE povtpo.products_options_id = '" . (int)$attribute['dependson_options_id'] . "' 
                                                AND povtpo.products_options_values_id = pov.products_options_values_id 
                                                AND pov.language_id = '" . (int)$languages_id . "' 
                                                ORDER BY pov.products_options_values_name");
                        while ($value = tep_db_fetch_array($values)) {
                            echo '<option value="' . $value['products_options_values_id'] . '" ' . ($attribute['dependson_options_values_id'] == $value['products_options_values_id'] ? 'selected' : '') . '>' . $value['products_options_values_name'] . '</option>';
                        }
                    }
                    ?>
                </select>
                <div id="attributes_dependson_values_id_btns_container" class="btn-group"></div>
            </div>
            <div id="attributes_dependson_container" class="col-xs-5">
                <label>Depends On: </label>
                <select id="attributes_dependson_options_id"
                        name="attributes_dependson_options_id"
                        hx-vals=\'{"productsid": "' . (int)$products_id . '", "action": "product_attributes_getDependsOnValueList"}\'
                class="form-control"
                hx-get="api_h.php"
                hx-include="this"
                hx-target="#attributes_dependson_values_id"
                hx-trigger="change">
                <option disabled selected value>Options</option>
                <option>None</option>';
                <?php
                while ($options_values = tep_db_fetch_array($options)) {?>
                    <option name="attributes_dependson_<?= $options_values['products_options_name'] ?>" value="<?= $options_values['products_options_id'] ?>" <?= ($attribute && $attribute['dependson_options_id'] == $options_values['products_options_id'] ? 'selected' : '') ?>><?= $options_values['products_options_name'] ?></option>';
                <?php } ?>
                </select>
                <select multiple id="attributes_dependson_values_id" class="form-control">
                    <option disabled selected value>Values</option>
                    <?php
                    if ($attribute) {
                        $values = tep_db_query("SELECT pov.products_options_values_id, pov.products_options_values_name 
                                                FROM products_options_values pov, products_options_values_to_products_options povtpo 
                                                WHERE povtpo.products_options_id = '" . (int)$attribute['dependson_options_id'] . "' 
                                                AND povtpo.products_options_values_id = pov.products_options_values_id 
                                                AND pov.language_id = '" . (int)$languages_id . "' 
                                                ORDER BY pov.products_options_values_name");
                        while ($value = tep_db_fetch_array($values)) {
                            echo '<option value="' . $value['products_options_values_id'] . '" ' . ($attribute['dependson_options_values_id'] == $value['products_options_values_id'] ? 'selected' : '') . '>' . $value['products_options_values_name'] . '</option>';
                        }
                    }
                    ?>
                </select>
                <div id="attributes_dependson_values_id_btns_container" class="btn-group"></div>
            </div>
            <div id="attributes_submit" class="col-xs-1 form-group">
                <label>Save: </label>
                <button id="attributes_insert_btn" class="btn btn-primary pull-right" type="button"
                    hx-post="api_h.php?action=product_attributes_addToProduct"
                    hx-include="#attributes_options_id,#attributes_values_id,#attributes_attribute_default,#attributes_dependson_options_id,#attributes_dependson_values_id"
                    hx-target="#attributesTable_body"
                    hx-swap="beforeend"
                    hx-indicator="#indicatorLines">Insert</button>
            </div>
        </div>
    </div>
    <?php
    return ob_get_clean();
}




function tcs_draw_variations_table($products_attributes, $products_id){
    // Define columns for the table
    $columns = [
        ['name' => '&nbsp;', 'class' => '', 'params' => ''],
        ['name' => 'Model', 'class' => '', 'params' => ''],
        ['name' => 'GTIN', 'class' => '', 'params' => ''],
        ['name' => 'Image ID', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Price', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Options', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Sort Order', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Autodesk Link', 'class' => 'text-center', 'params' => ''],
        ['name' => 'Actions', 'class' => 'text-center', 'params' => ['colspan' => '2']],
    ];

    // Get the variations
    $variations = $products_attributes->get_variations();
    //print_rr($products_attributes);
    $rows = '';
    foreach ($variations as $key_a => $variation) {
        $rows .= tcs_draw_variations_table_row($variation);
    }
    $footer = "<div class='panel-footer varitionsFooter'>
        <div id='variations_options_container' class=''>
            <div class='row'>";
    $footer .= tcs_draw_variations_form($products_attributes, $products_id);
    $footer .= "</div></div></div>";
    $datashix = [
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    ];
    //print_rr($datashix, 'datashix');
    // Output the table
    return tcs_draw_admin_bootstrap_table(
        'Variations Table',
        'variations_table',
        'table table-striped table-draggable',
        $columns,
        $rows,
        $footer
    );
}


function tcs_draw_variations_table_row($variation,$params = []){
    print_rr($variation,'variations');
    $attributes = explode('{', substr($variation['attribute_string'], strpos($variation['attribute_string'], '{') + 1));
    $attributesNameString = "";
    for ($i = 0, $n = sizeof($attributes); $i < $n; $i++) {
        $pair = explode('}', $attributes[$i]);
        $attributesNameString .= '<span style="font-weight:bold">' . tep_options_name($pair[0]) . ':</span> ';
        $attributesNameString .= tep_values_name($pair[1]) . ' ';
    }

    $rowId = $variation['products_variations_id'];
    $rowClass = ($variation['enabled'] == "0") ? "table-danger danger" : "variation_row";
    $autodesk_link = "";
    $autodesk_link = $variation['autodesk_link_name'];
    if ($variation['autodesk_link'] != "") {
        $autodesk_link = $autodesk_link;
    }
    // Add this row's data to the dataset
    $id = "variations_table_row_{$rowId}";

    $row_content = [
        ['class' => 'portletTD',          'id' => '',                                     'content' => '<div class="portlet">&nbsp;</div>'],
        ['class' => '',                   'id' => "variations_table_model_{$rowId}",      'content' => $variation['model']],
        ['class' => '',                   'id' => "variations_table_gtin_{$rowId}",       'content' => $variation['gtin']],
        ['class' => 'text-center',        'id' => "variations_table_image_id_{$rowId}",   'content' => $variation['image_id']],
        ['class' => 'text-center',        'id' => "variations_table_Price_{$rowId}",      'content' => $variation['price']],
        ['class' => '',                   'id' => "variations_table_attributes_{$rowId}", 'content' => $attributesNameString],
        ['class' => '',                   'id' => '',                                     'content' => '<input class="indexSO variationsSOinput" type="text" name="" size="1" maxlength="4" value="' . $variation['sort_order'] . '">'],
        ['class' => '',                   'id' => '',                                     'content' => $autodesk_link],
        ['class' => 'listsOptionsEdit',   'id' => '',                                     'content' => 'e', 'hx-target' => '#variations_form',
            'hx-get'=> 'api_h.php?action=product_variations_product_edit',
            'hx-vals' => '{"products_id":"' . $variation['products_id'] . '", "products_variations_id":"' . $variation['products_variations_id'] . '"}'],
        ['class' => 'listsOptionsDelete', 'id' => '',                                     'text' => 'x', 'hx-target' => "#{$id}",
            'hx-get'=> 'api_h.php?action=product_variations_removeVariation',
            'hx-vals' => '{"products_variations_id":"' . $variation['products_variations_id'] . '"}'],
    ];
    //print_rr($params,'paramamaamama');
    $params['data-variationsid'] = $rowId;
    // Output the table
    return tcs_draw_admin_bootstrap_table_row($id, $rowClass, $row_content,$params);
}


function tcs_draw_variations_form($id, $products_id, $values = [])
{
    $products_options_name_query = tep_db_query("select distinct popt.products_options_id, popt.products_options_name from products_options popt, products_attributes patrib where patrib.products_id='" . $products_id . "' and patrib.options_id = popt.products_options_id order by popt.products_options_name");
    $select_output_selects = [
        "<common>" => [
            "class" => "form-control variations_form"
        ]
    ];
    if (tep_db_num_rows($products_options_name_query)) {
        while ($products_options_name = tep_db_fetch_array($products_options_name_query)) {
            $products_options_query = tep_db_query("select pov.products_options_values_id, pov.products_options_values_name, pa.options_values_price, pa.price_prefix, pa.attribute_default, pa.dependson_options_id, pa.dependson_options_values_id from products_attributes pa, products_options_values pov where pa.products_id = '" . $products_id . "' and pa.options_id = '" . (int)$products_options_name['products_options_id'] . "' and pa.options_values_id = pov.products_options_values_id order by pa.products_attributes_sort_order");
            $select_id = "attribute[{$products_options_name['products_options_id']}]";
            $select_output_selects[$select_id] = [
                "node_type" => "select",
                "label" => $products_options_name['products_options_name'],
                "options" => []
            ];
            while ($products_options = tep_db_fetch_array($products_options_query)) {
                $select_output_selects[$select_id]['options'][$products_options['products_options_values_id']] = $products_options['products_options_values_name'];
            }
        }
    }


    $array = [
        "form" => [
            "<cfg>" => [
                "common" => [
                    "name" => "name_<node_type>",
                    "id" => "<name>_<node_type>",
                    "label" => "<name>",
                    "value" => "<values_list>"
                ],
                "noTag" => true,
                "id" => "variations_form",
                "class" => "variations_form"
            ],
            "inputs" => [
                "node_type" => "group",
                "class" => "col-sm-3 well",
                "elements" => [
                    "<common>" => [
                        "node_type" => "input",
                        "class" => "form-control variations_form",
                    ],
                    "model" => [],
                    "GTIN" => [],
                    "image_id" => [],
                    "price" => [],
                    "products_id" => [
                        "type" => "hidden",
                        "value" => $products_id
                    ]
                ]
            ],
            "right_elements" => [
                "node_type" => "group",
                "class" => "col-sm-9",
                "elements" => [
                    "attributes" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => $select_output_selects
                    ],
                    "autodesk_linking" => [
                        "node_type" => "group",
                        "class" => "col-sm-6 well",
                        "elements" => [
                            "<common>" => [
                                "class" => "form-control variations_form"
                            ],
                            "terms" => [
                                "node_type" => "input",
                                "label" => ""
                            ],
                            "search" => [
                                "node_type" => "button",
                                "hx-trigger" => "click",
                                "hx-get" => "api_h.php?action=product_autodesk_link_search",
                                "hx-target" => "#autodesk_link_select",
                                "hx-include" => "#terms_input",
                                "hx-swap" => "innerHTML"
                            ],
                            "autodesk_link" => [
                                "node_type" => "select"
                            ]
                        ]
                    ]
                ]
            ],
            "controls" => [
                "node_type" => "group",
                "class" => "col-sm-9 well text-right",
                "elements" => [
                    "submit" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_save",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_table_body",
                        "hx-swap" => "beforeend"
                    ],
                    "cancel" => [
                        "node_type" => "button",
                        "hx-trigger" => "click",
                        "hx-get" => "api_h.php?action=product_cancel",
                        "hx-indicator" => "#indicatorLines",
                        "hx-include" => ".variations_form",
                        "hx-target" => "#variations_form",
                        "hx-swap" => "outerHTML"
                    ]
                ]
            ]
        ]
    ];
    ////print_rr($array,"renderarray");
    return renderForm($array, $values);

}

