<?php
// Test script to verify the API fix - simplified version
echo "Testing the fixed methods...\n\n";

// Test the row ID generation logic
function test_row_id_generation() {
    echo "Testing row ID generation:\n";

    // Simulate attribute data as it would come from the database
    $attribute = [
        'products_attributes_id' => 3578,
        'options_id' => 1,
        'options_values_id' => 199,
        'products_options_name' => 'Size',
        'products_options_values_name' => '44in',
        'attribute_default' => 0,
        'dependson_options_id' => null,
        'dependson_options_values_id' => null,
        'products_attributes_sort_order' => 0
    ];

    // Extract the data with proper field names (same logic as in the fixed function)
    $options_name = $attribute['products_options_name'] ?? '';
    $options_id = $attribute['options_id'] ?? $attribute['products_options_id'] ?? '';
    $values_name = $attribute['products_options_values_name'] ?? '';
    $values_id = $attribute['options_values_id'] ?? $attribute['products_options_values_id'] ?? '';

    $rowId = $options_id . '_' . $values_id;
    $id = "attributesTableRow_{$rowId}";

    echo "Options ID: $options_id\n";
    echo "Values ID: $values_id\n";
    echo "Row ID: $rowId\n";
    echo "Full ID: $id\n";
    echo "Expected: attributesTableRow_1_199\n";
    echo "Match: " . ($id === "attributesTableRow_1_199" ? "YES" : "NO") . "\n\n";

    return $id;
}

// Test HTML generation with proper IDs
function test_html_generation() {
    echo "Testing HTML generation:\n";

    $attribute = [
        'products_attributes_id' => 3578,
        'options_id' => 1,
        'options_values_id' => 199,
        'products_options_name' => 'Size',
        'products_options_values_name' => '44in',
        'attribute_default' => 0,
        'dependson_options_id' => null,
        'dependson_options_values_id' => null,
        'products_attributes_sort_order' => 0
    ];

    $options_id = $attribute['options_id'];
    $values_id = $attribute['options_values_id'];
    $rowId = $options_id . '_' . $values_id;

    // Generate the expected HTML elements
    $expected_elements = [
        "attributesTableRow_{$rowId}",
        "attributesTable_optionsName_{$rowId}",
        "attributesTable_optionsValueName_{$rowId}",
        "attributesTable_optionsValuePriceDefault_{$rowId}",
        "attributesTable_DependsOn_{$rowId}"
    ];

    echo "Expected HTML element IDs:\n";
    foreach ($expected_elements as $element) {
        echo "- $element\n";
    }

    echo "\nThese should replace the empty IDs like 'attributesTableRow__'\n";
}

// Run the tests
test_row_id_generation();
test_html_generation();

echo "Fix Summary:\n";
echo "1. Added missing methods to tcs_product_attributes class:\n";
echo "   - get_attribute_from_id()\n";
echo "   - get_attribute_from_values()\n";
echo "   - insert_or_update_attribute()\n";
echo "   - check_dependency_issues()\n\n";
echo "2. Fixed tcs_draw_attributes_table_row() to handle field names properly\n";
echo "3. Added null coalescing operators to prevent undefined index errors\n\n";
echo "The API should now return proper HTML with correct IDs instead of empty placeholders.\n";
?>
