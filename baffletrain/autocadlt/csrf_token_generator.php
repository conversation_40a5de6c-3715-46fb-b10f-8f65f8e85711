<?php
/*
  CSRF Token Generator for Admin AJAX Requests
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2024 osCommerce

  Released under the GNU General Public License
*/

require('includes/application_top.php');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(array('error' => 'Method not allowed'));
    exit;
}

// Get action parameter
$action = isset($_POST['action']) ? $_POST['action'] : 'admin_default';

// Generate new CSRF token
$token = tep_csrf_token($action);

// Return token as JSON
header('Content-Type: application/json');
echo json_encode(array(
    'token' => $token,
    'field_name' => 'csrf_token',
    'action' => $action,
    'timestamp' => time()
));
?>
