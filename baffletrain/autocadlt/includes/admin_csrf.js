/**
 * Admin CSRF Protection JavaScript
 * Automatically handles CSRF tokens for AJAX requests and dynamic forms
 */

var AdminCSRFManager = {
    // Configuration
    config: {
        tokenFieldName: 'csrf_token',
        metaTagName: 'csrf-token',
        headerName: 'X-CSRF-Token',
        autoProtectForms: true,
        autoProtectAjax: true,
        debug: false
    },

    // Get CSRF token from meta tag
    getToken: function(action) {
        action = action || 'admin_default';
        var metaTag = document.querySelector('meta[name="' + this.config.metaTagName + '"]');
        if (metaTag) {
            return metaTag.getAttribute('content');
        }
        return null;
    },

    // Generate new token via AJAX
    generateToken: function(action, callback) {
        action = action || 'admin_default';
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'csrf_token_generator.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.token) {
                        // Update meta tag
                        var metaTag = document.querySelector('meta[name="' + AdminCSRFManager.config.metaTagName + '"]');
                        if (metaTag) {
                            metaTag.setAttribute('content', response.token);
                        }
                        if (callback) callback(response.token);
                    }
                } catch (e) {
                    if (AdminCSRFManager.config.debug) {
                        console.error('CSRF token generation failed:', e);
                    }
                }
            }
        };
        xhr.send('action=' + encodeURIComponent(action));
    },

    // Add CSRF token to form
    protectForm: function(form, action) {
        if (!form) return false;
        
        action = action || 'admin_default';
        var token = this.getToken(action);
        if (!token) return false;

        // Remove existing CSRF token field
        var existingField = form.querySelector('input[name="' + this.config.tokenFieldName + '"]');
        if (existingField) {
            existingField.remove();
        }

        // Add new CSRF token field
        var tokenField = document.createElement('input');
        tokenField.type = 'hidden';
        tokenField.name = this.config.tokenFieldName;
        tokenField.value = token;
        form.appendChild(tokenField);

        return true;
    },

    // Protect all forms with class 'csrf-protected'
    protectAllForms: function() {
        var forms = document.querySelectorAll('form.csrf-protected, form[data-csrf="true"]');
        for (var i = 0; i < forms.length; i++) {
            var action = forms[i].getAttribute('data-csrf-action') || 'admin_default';
            this.protectForm(forms[i], action);
        }
    },

    // Setup AJAX protection
    setupAjaxProtection: function() {
        var self = this;
        
        // jQuery AJAX setup (if jQuery is available)
        if (typeof $ !== 'undefined' && $.ajaxSetup) {
            $.ajaxSetup({
                beforeSend: function(xhr, settings) {
                    // Only add token to POST requests
                    if (settings.type && settings.type.toUpperCase() === 'POST') {
                        var token = self.getToken();
                        if (token) {
                            xhr.setRequestHeader(self.config.headerName, token);
                            
                            // Also add to data if it's form data
                            if (settings.data && typeof settings.data === 'string') {
                                settings.data += '&' + self.config.tokenFieldName + '=' + encodeURIComponent(token);
                            } else if (settings.data && typeof settings.data === 'object') {
                                settings.data[self.config.tokenFieldName] = token;
                            }
                        }
                    }
                }
            });
        }

        // Native XMLHttpRequest protection
        var originalOpen = XMLHttpRequest.prototype.open;
        var originalSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            this._method = method;
            this._url = url;
            return originalOpen.apply(this, arguments);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            if (this._method && this._method.toUpperCase() === 'POST') {
                var token = self.getToken();
                if (token) {
                    this.setRequestHeader(self.config.headerName, token);
                    
                    // Add token to form data
                    if (data && typeof data === 'string') {
                        data += '&' + self.config.tokenFieldName + '=' + encodeURIComponent(token);
                    }
                }
            }
            return originalSend.call(this, data);
        };
    },

    // Handle CSRF validation errors
    handleCSRFError: function(xhr, textStatus, errorThrown) {
        if (xhr.status === 403) {
            try {
                var response = JSON.parse(xhr.responseText);
                if (response.code === 'CSRF_VALIDATION_FAILED') {
                    alert('Security validation failed. The page will be refreshed.');
                    window.location.reload();
                    return;
                }
            } catch (e) {
                // Not JSON response
            }
        }
        
        // Default error handling
        if (this.config.debug) {
            console.error('CSRF Error:', xhr, textStatus, errorThrown);
        }
    },

    // Initialize CSRF protection
    init: function(options) {
        // Merge options
        if (options) {
            for (var key in options) {
                if (options.hasOwnProperty(key)) {
                    this.config[key] = options[key];
                }
            }
        }

        var self = this;

        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                self.initializeProtection();
            });
        } else {
            this.initializeProtection();
        }
    },

    // Initialize protection after DOM is ready
    initializeProtection: function() {
        if (this.config.autoProtectForms) {
            this.protectAllForms();
        }

        if (this.config.autoProtectAjax) {
            this.setupAjaxProtection();
        }

        // Setup global error handler for jQuery
        if (typeof $ !== 'undefined') {
            var self = this;
            $(document).ajaxError(function(event, xhr, settings, thrownError) {
                self.handleCSRFError(xhr, 'error', thrownError);
            });
        }

        // Re-protect forms when new content is added (for HTMX)
        if (typeof htmx !== 'undefined') {
            var self = this;
            htmx.onLoad(function(content) {
                var forms = content.querySelectorAll('form.csrf-protected, form[data-csrf="true"]');
                for (var i = 0; i < forms.length; i++) {
                    var action = forms[i].getAttribute('data-csrf-action') || 'admin_default';
                    self.protectForm(forms[i], action);
                }
            });
        }

        if (this.config.debug) {
            console.log('Admin CSRF Protection initialized');
        }
    }
};

// Auto-initialize when script loads
AdminCSRFManager.init();
