<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/

// Start the clock for the page parse time log
  define('PAGE_PARSE_START_TIME', microtime());

// Set the level of error reporting
  error_reporting(E_ALL & ~E_NOTICE);
  
  if (defined('E_DEPRECATED')) {
    error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_WARNING);
  }

// check support for register_globals
  if (function_exists('ini_get') && (ini_get('register_globals') == false) && (PHP_VERSION < 4.3) ) {
    exit('Server Requirement Error: register_globals is disabled in your PHP configuration. This can be enabled in your php.ini configuration file or in the .htaccess file in your catalog directory. Please use PHP 4.3+ if register_globals cannot be enabled on the server.');
  }

// load server configuration parameters
  if (file_exists('includes/local/configure.php')) { // for developers
    include('includes/local/configure.php');
  } else {
    include('includes/configure.php');
  }

// Define the project version --- obsolete, now retrieved with tep_get_version()
  define('PROJECT_VERSION', 'osCommerce Online Merchant v2.3');

// some code to solve compatibility issues
// require('includes/functions/compatibility.php');

// set the type of request (secure or not)
  $request_type = (getenv('HTTPS') == 'on') ? 'SSL' : 'NONSSL';

// set php_self in the local scope
  $req = parse_url($_SERVER['SCRIPT_NAME']);
  $PHP_SELF = substr($req['path'], ($request_type == 'SSL') ? strlen(DIR_WS_HTTPS_ADMIN) : strlen(DIR_WS_ADMIN));

// Used in the "Backup Manager" to compress backups
  define('LOCAL_EXE_GZIP', 'gzip');
  define('LOCAL_EXE_GUNZIP', 'gunzip');
  define('LOCAL_EXE_ZIP', 'zip');
  define('LOCAL_EXE_UNZIP', 'unzip');

// include the list of project database tables
  require('includes/database_tables.php');

// Define how do we update currency exchange rates
// Possible values are 'oanda' 'xe' 'fixer' or ''
// fixer is the lastest added, more details at http://fixer.io
  define('CURRENCY_SERVER_PRIMARY', 'fixer');
  define('CURRENCY_SERVER_BACKUP', '');

// include the database functions
  require(DIR_FS_CATALOG . 'includes/functions/database.php');

// make a connection to the database... now
  tep_db_connect() or die('Unable to connect to database server!');

// Define cache file path
$cache_file = DIR_FS_CACHE . 'configuration_cache.php';


// Check if cache file exists and is still valid (for example, 1 day old)
if (!file_exists($cache_file)) {   
    // Cache file is not valid or doesn't exist, create it
    $cache_data = '<?php ';
    
    // Run the database query
    $configuration_query = tep_db_query('SELECT configuration_key AS cfgKey, MIN(configuration_value) AS cfgValue FROM configuration GROUP BY configuration_key');
    while ($configuration = tep_db_fetch_array($configuration_query)) {
        // Define constants based on configuration        
        // Prepare cache content to be written to the file
         if (!defined($configuration['cfgKey'])) $cache_data .= " if (!defined({$configuration['cfgKey']})) define('{$configuration['cfgKey']}','" . addslashes($configuration['cfgValue']) . "');";
    }
    
    // Close PHP tag
    $cache_data .= '?>';
    
    // Write cache to file
    file_put_contents($cache_file, $cache_data);    
    // Now include the newly created cache file    
}
include($cache_file);
// define our general functions used application-wide
  require('includes/functions/general.php');
  require('includes/functions/html_output.php');
  require('includes/functions/tcs_functions.php');

// initialize the logger class
  require('includes/classes/logger.php');
// initialize the attributes class
  require(DIR_FS_CATALOG . 'includes/classes/attributes.class.php');
  
  // composer autoloader
  require(DIR_FS_CATALOG . 'vendor/autoload.php');


//initialize the autocaddesk class
  require(DIR_FS_CATALOG . 'includes/classes/autodesk_wsapi_guzzle.php');

// include shopping cart class
  require('includes/classes/shopping_cart.php');

// define how the session functions will be used
  require('includes/functions/sessions.php');

// include enhanced security functions for admin
  require('includes/functions/security.php');

// set the cookie domain
  $cookie_domain = (($request_type == 'NONSSL') ? HTTP_COOKIE_DOMAIN : HTTPS_COOKIE_DOMAIN);
  $cookie_path = (($request_type == 'NONSSL') ? HTTP_COOKIE_PATH : HTTPS_COOKIE_PATH);

// set the session name and save path
  tep_session_name('osCAdminID');
  tep_session_save_path(SESSION_WRITE_DIRECTORY);

// set the session cookie parameters
   if (function_exists('session_set_cookie_params')) {
    session_set_cookie_params(0, $cookie_path, $cookie_domain);
  } elseif (function_exists('ini_set')) {
    ini_set('session.cookie_lifetime', '0');
    ini_set('session.cookie_path', $cookie_path);
    ini_set('session.cookie_domain', $cookie_domain);
  }

  @ini_set('session.use_only_cookies', (SESSION_FORCE_COOKIE_USE == 'True') ? 1 : 0);

// lets start our session
  tep_session_start();

  if ( (PHP_VERSION >= 4.3) && function_exists('ini_get') && (ini_get('register_globals') == false) ) {
    extract($_SESSION, EXTR_OVERWRITE+EXTR_REFS);
  }

// initialize a session token for admin (legacy support)
  if (!tep_session_is_registered('sessiontoken')) {
    $sessiontoken = md5(tep_rand() . tep_rand() . tep_rand() . tep_rand());
    tep_session_register('sessiontoken');
  }

// initialize CSRF protection for admin
  if (tep_csrf_protection_enabled()) {
    // Clean up expired CSRF tokens periodically
    if (mt_rand(1, 100) <= 5) { // 5% chance to run cleanup
      tep_csrf_token_cleanup();
    }
  }

// set the language
  if (!tep_session_is_registered('language') || isset($_GET['language'])) {
    if (!tep_session_is_registered('language')) {
      tep_session_register('language');
      tep_session_register('languages_id');
    }

    include('includes/classes/language.php');
    $lng = new language();

    if (isset($_GET['language']) && @tep_not_null($_GET['language'])) {
      $lng->set_language($_GET['language']);
    } else {
      $lng->get_browser_language();
    }

    $language = $lng->language['directory'];
    $languages_id = $lng->language['id'];
  }


// redirect to login page if administrator is not yet logged in
  if (!tep_session_is_registered('admin')) {
    $redirect = false;

    $current_page = $PHP_SELF;
// if the first page request is to the login page, set the current page to the index page
// so the redirection on a successful login is not made to the login page again
    if ( ($current_page == 'login.php') && !tep_session_is_registered('redirect_origin') ) {
      $current_page = 'index.php';
      $_GET = array();
    }

    if ($current_page != 'login.php') {
      if (!tep_session_is_registered('redirect_origin')) {
        tep_session_register('redirect_origin');

        $redirect_origin = array('page' => $current_page,
                                 'get' => $_GET);
      }

// try to automatically login with the HTTP Authentication values if it exists
      if (!tep_session_is_registered('auth_ignore')) {
        if (isset($_SERVER['PHP_AUTH_USER']) && !empty($_SERVER['PHP_AUTH_USER']) && isset($_SERVER['PHP_AUTH_PW']) && !empty($_SERVER['PHP_AUTH_PW'])) {
          $redirect_origin['auth_user'] = $_SERVER['PHP_AUTH_USER'];
          $redirect_origin['auth_pw'] = $_SERVER['PHP_AUTH_PW'];
        }
      }

      $redirect = true;
    }

    if (!isset($login_request) || isset($_GET['login_request']) || isset($_POST['login_request']) || isset($_COOKIE['login_request']) || isset($_SESSION['login_request']) || isset($_FILES['login_request']) || isset($_SERVER['login_request'])) {
      $redirect = true;
    }

    if ($redirect == true) {
      tep_redirect(tep_href_link('login.php', (isset($redirect_origin['auth_user']) ? 'action=process' : '')));
    }

    unset($redirect);
  }

// include the language translations
  $_system_locale_numeric = setlocale(LC_NUMERIC, 0);
  require('includes/languages/' . $language . '.php');
  setlocale(LC_NUMERIC, $_system_locale_numeric); // Prevent LC_ALL from setting LC_NUMERIC to a locale with 1,0 float/decimal values instead of 1.0 (see bug #634)

  $current_page = basename($PHP_SELF);
  if (file_exists('includes/languages/' . $language . '/' . $current_page)) {
    include('includes/languages/' . $language . '/' . $current_page);
  }

// define our localization functions
  require('includes/functions/localization.php');

// Include validation functions (right now only email address)
  require('includes/functions/validations.php');

// setup our boxes
  require('includes/classes/table_block.php');
  require('includes/classes/box.php');

// initialize the message stack for output messages
  require('includes/classes/message_stack.php');
  $messageStack = new messageStack;

// split-page-results
  require('includes/classes/split_page_results.php');

// entry/item info classes
  require('includes/classes/object_info.php');

// email classes
  require('includes/classes/mime.php');
  require('includes/classes/email.php');

// file uploading class
  require('includes/classes/upload.php');

// action recorder
  require('includes/classes/action_recorder.php');

// calculate category path
  if (isset($_GET['cPath'])) {
    $cPath = $_GET['cPath'];
  } else {
    $cPath = '';
  }

  if (@tep_not_null($cPath)) {
    $cPath_array = tep_parse_category_path($cPath);
    $cPath = implode('_', $cPath_array);
    $current_category_id = end($cPath_array);
  } else {
    $current_category_id = 0;
  }

// initialize configuration modules
  require('includes/classes/cfg_modules.php');
  $cfgModules = new cfg_modules();

// the following cache blocks are used in the Tools->Cache section
// ('language' in the filename is automatically replaced by available languages)
  $cache_blocks = array(array('title' => TEXT_CACHE_CATEGORIES, 'code' => 'categories', 'file' => 'categories_box-language.cache', 'multiple' => true),
                        array('title' => TEXT_CACHE_MANUFACTURERS, 'code' => 'manufacturers', 'file' => 'manufacturers_box-language.cache', 'multiple' => true),
                        array('title' => TEXT_CACHE_ALSO_PURCHASED, 'code' => 'also_purchased', 'file' => 'also_purchased-language.cache', 'multiple' => true)
                       );
$cache_blocks[] = array ( 
          'title' => 'categories_superfish', 
          'code' => 'categories_superfish', 
          'file' => 'categories_superfish_box-language.cache', 
          'multiple' => true
        );
					   
					   
  require(DIR_FS_CATALOG . 'includes/classes/hooks.php');
  $OSCOM_Hooks = new hooks('admin');

  $OSCOM_Hooks->register(basename($PHP_SELF, '.php'));
  