<?php
/*
  $Id$

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2014 osCommerce

  Released under the GNU General Public License
*/
?>
<!DOCTYPE html>
<html <?php echo HTML_PARAMS; ?>>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=<?php echo CHARSET; ?>">
<meta name="robots" content="noindex,nofollow">
<title><?php echo TITLE; ?></title>
<base href="<?php echo ($request_type == 'SSL') ? HTTPS_SERVER . DIR_WS_HTTPS_ADMIN : HTTP_SERVER . DIR_WS_ADMIN; ?>" />
<?php echo tep_draw_csrf_meta_tag('admin_default'); ?>
<link type="text/css" rel="stylesheet" href="/min/f=ext/jquery/ui/redmond/jquery-ui-1.10.4.min.css" />
<script src="https://unpkg.com/htmx.org@2.0.2"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<!-- Alpine.js Transitions CSS -->
<link rel="stylesheet" href="alpine-htmx-transitions.css">
<!-- Alpine.js - MUST be loaded before our transitions script -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<?php 
/*
<!--[if IE]><script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/flot/excanvas.min.js', '', 'SSL'); ?>"></script><![endif]-->
<link rel="stylesheet" type="text/css" href="<?php echo tep_catalog_href_link('ext/jquery/ui/redmond/jquery-ui-1.10.4.min.css', '', 'SSL'); ?>">
<script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/jquery/jquery-2.2.3.min.js', '', 'SSL'); ?>"></script>
<script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/jquery/ui/jquery-ui-1.10.4.min.js', '', 'SSL'); ?>"></script>

<?php
  if (@tep_not_null(JQUERY_DATEPICKER_I18N_CODE)) {
?>
<script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/jquery/ui/i18n/jquery.ui.datepicker-' . JQUERY_DATEPICKER_I18N_CODE . '.js', '', 'SSL'); ?>"></script>
<script type="text/javascript">
$.datepicker.setDefaults($.datepicker.regional['<?php echo JQUERY_DATEPICKER_I18N_CODE; ?>']);
</script>
<?php
  }
  
<script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/flot/jquery.flot.min.js', '', 'SSL'); ?>"></script>
<script type="text/javascript" src="<?php echo tep_catalog_href_link('ext/flot/jquery.flot.time.min.js', '', 'SSL'); ?>"></script>
<link rel="stylesheet" type="text/css" href="includes/stylesheet.css">
<script type="text/javascript" src="includes/general.js"></script>
*/
?>
<link type="text/css" rel="stylesheet" href="/min/g=admin.css" />
<?php
//script type="text/javascript" src="/min/g=admin.js&hash=1"></script>

  require(DIR_FS_CATALOG . '/min/utils.php');
  echo '<script src="'.Minify_getUri('admin.js').'"></script>'
 ?>
<script type="text/javascript" src="<?php echo tep_href_link('ext/ckeditor4/ckeditor.js'); ?>"></script>
<script type="text/javascript" src="<?php echo tep_href_link('ext/ckeditor4/adapters/jquery.js'); ?>"></script>

</head>
<body>
<div class="hidden">
    <img alt="Result loading..." id="indicatorLines" width="25" src="images/bars.svg"/>
</div>
<?php require('includes/header.php'); ?>

<?php
  if (tep_session_is_registered('admin')) {
    include('includes/column_left.php');
  } else {
?>

<style>
#contentText {
  margin-left: 0;
}
</style>

<!-- Admin CSRF Protection JavaScript -->
<script src="includes/admin_csrf.js"></script>

<?php
  }
?>

<div id="contentText">
