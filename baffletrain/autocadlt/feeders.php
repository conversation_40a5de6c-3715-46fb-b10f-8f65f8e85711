<?php
/*
  $Id: feeders.php,v 1.6.2 (<PERSON>) 2007-01-01 13:13:49 dgw_ Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

  require('includes/template_top.php');
?>

    <table border="0" width="100%" cellspacing="0" cellpadding="2">
      <tr>
        <td><table border="0" width="100%" cellspacing="0" cellpadding="2" height="40">
          <tr>
            <td class="pageHeading"><?php echo 'Store Feeds'?>
      </tr>
      <tr>
        <td class="main"><p><strong>The  &quot;store feeds&quot; listed below, are the ideal way to further promote your oscommerce products catalog by automatically submitting to the providers below. Each provider lists your products on their searchable website, and allows customers to click back to your website to purchase. </strong></p>
          <p>1. Sign-up for a provider below.<br />
            2. Click <a href="configuration.php?gID=62" target="_blank" class="splitPageLink"><strong>[HERE]</strong></a> to update your 'store feed' accounts.<br />
            3. Click on the corresponding START FEED link below, or the name of the provider in the left box.
            <br />
            4. Upon completion, you will receive a success message on screen. </p>
          </td>
      </tr>
      <tr>
        <td><br />
          <table width="100%" border="1" cellpadding="2" cellspacing="0" bordercolor="#FFFFFF">
          <tr valign="top" class="main">
            <td width="10%" align="right" bgcolor="#080381" class="menuBoxHeading"><strong> BizRate: </strong></td>
            <td width="53%" bgcolor="#EFEFEF" class="main"><p>Registering will allow you to participate in the BizRate Merchant ratings program as well as list your products on BizRate.com, now part of the Shopzilla.com sites. </p>
              <p><strong>FEE BASED</strong></p></td>
            <td width="11%" align="center" bgcolor="#EFEFEF"><?php echo '<a href="' . tep_href_link('bizrate.php') . '" target="_blank" class="splitPageLink"><strong>RUN FEED</strong></a>'; ?></td>
            <td width="11%" align="center" bgcolor="#EFEFEF">
			<?php 
			$OutFile1 = DIR_FS_DOCUMENT_ROOT."feeds/".BIZRATE_FILENAME; 
			if ( file_exists( $OutFile1 ) ) 
			{ 
			;?>
			<a href="<?php echo tep_href_link("../feeds/".BIZRATE_FILENAME); ?>" target="_blank" class="splitPageLink"><strong>DOWNLOAD</strong></a><br />
              <font size="1">(right click and do 'save target as')</font>
			  <?php } ;?>&nbsp;
			  </td>
            <td width="8%" align="center" bgcolor="#EFEFEF"><a href="http://merchant.shopzilla.com/oa/registration/" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td width="7%" align="center" bgcolor="#EFEFEF"><a href="https://merchant.shopzilla.com/" target="_blank" class="splitPageLink"><strong>LOGIN</strong></a></td>
          </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong> Froogle US (Google Base US): </strong></td>
            <td bgcolor="#EFEFEF" class="main"><p>It costs nothing to reach online shoppers by listing your items with Froogle, Google's free shopping search engine. You can link directly to your website. </p>
              <p><strong>FREE</strong></p></td>
            <td align="center" bgcolor="#EFEFEF"><?php echo '<a href="' . tep_href_link('froogle_us.php') . '" target="_blank" class="splitPageLink"><strong>RUN FEED</strong></a>'; ?></td>
            <td align="center" bgcolor="#EFEFEF"><?php 
			$OutFile1 = DIR_FS_DOCUMENT_ROOT."feeds/".FROOGLE_FTP_FILENAME; 
			if ( file_exists( $OutFile1 ) ) 
			{ 
			;?>
              <a href="<?php echo tep_href_link("../feeds/".FROOGLE_FTP_FILENAME); ?>" target="_blank" class="splitPageLink"><strong>DOWNLOAD</strong></a><br />
              <font size="1">(right click and do 'save target as')</font>
              <?php } ;?>
              &nbsp; </td>
            <td align="center" bgcolor="#EFEFEF"><a href="https://www.google.com/base/welcome" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://base.google.com/base/welcome?conturl=http://base.google.com/base/dashboard&hl=en_US" target="_blank" class="splitPageLink"><strong>LOGIN</strong></a></td>
          </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong> Froogle UK (Google Base UK): </strong></td>
            <td bgcolor="#EFEFEF" class="main"><p>It costs nothing to reach online shoppers by listing your items with Froogle, Google's free shopping search engine. You can link directly to your website. </p>
              <p><strong>FREE</strong></p></td>
            <td align="center" bgcolor="#EFEFEF"><?php echo '<a href="' . tep_href_link('froogle_uk.php') . '" target="_blank" class="splitPageLink"><strong>RUN FEED</strong></a>'; ?></td>
            <td align="center" bgcolor="#EFEFEF"><?php 
			$OutFile1 = DIR_FS_DOCUMENT_ROOT."feeds/".FROOGLE_FTP_FILENAME; 
			if ( file_exists( $OutFile1 ) ) 
			{ 
			;?>
              <a href="<?php echo tep_href_link("../feeds/".FROOGLE_FTP_FILENAME); ?>" target="_blank" class="splitPageLink"><strong>DOWNLOAD</strong></a><br />
              <font size="1">(right click and do 'save target as')</font>
              <?php } ;?>
              &nbsp; </td>
            <td align="center" bgcolor="#EFEFEF"><a href="https://www.google.com/base/welcome" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://base.google.co.uk/base/welcome?conturl=http://base.google.co.uk/base/dashboard&hl=en_GB" target="_blank" class="splitPageLink"><strong>LOGIN</strong></a></td>
          </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong> Froogle DE (Google Base DE): </strong></td>
            <td bgcolor="#EFEFEF" class="main"><p>It costs nothing to reach online shoppers by listing your items with Froogle, Google's free shopping search engine. You can link directly to your website. </p>
              <p><strong>FREE</strong></p></td>
            <td align="center" bgcolor="#EFEFEF"><?php echo '<a href="' . tep_href_link('froogle_de.php') . '" target="_blank" class="splitPageLink"><strong>RUN FEED</strong></a>'; ?></td>
            <td align="center" bgcolor="#EFEFEF"><?php 
			$OutFile1 = DIR_FS_DOCUMENT_ROOT."feeds/".FROOGLE_FTP_FILENAME; 
			if ( file_exists( $OutFile1 ) ) 
			{ 
			;?>
              <a href="<?php echo tep_href_link("../feeds/".FROOGLE_FTP_FILENAME); ?>" target="_blank" class="splitPageLink"><strong>DOWNLOAD</strong></a><br />
              <font size="1">(right click and do 'save target as')</font>
              <?php } ;?>
              &nbsp; </td>
            <td align="center" bgcolor="#EFEFEF"><a href="https://www.google.com/base/welcome" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://base.google.de/base/welcome?conturl=http://base.google.de/base/dashboard&hl=de_DE " target="_blank" class="splitPageLink"><strong>LOGIN</strong></a></td>
          </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong> Yahoo: </strong></td>
            <td bgcolor="#EFEFEF" class="main"><p>Your products will be featured prominently in <A 
href="http://shopping.yahoo.com/" target="_blank" class="splitPageLink"><strong>Yahoo! Shopping</strong></A> and appear in highly relevant areas across Yahoo!, giving you access to millions of motivated buyers. </p>
              <p><strong>FEE BASED</strong></p></td>
            <td align="center" bgcolor="#EFEFEF"><?php echo '<a href="' . tep_href_link('yahoo.php') . '" target="_blank" class="splitPageLink"><strong>RUN FEED</strong></a>'; ?></td>
            <td align="center" bgcolor="#EFEFEF"><?php 
			$OutFile1 = DIR_FS_DOCUMENT_ROOT."feeds/".YAHOO_FTP_FILENAME; 
			if ( file_exists( $OutFile1 ) ) 
			{ 
			;?>
              <a href="<?php echo tep_href_link("../feeds/".YAHOO_FTP_FILENAME); ?>" target="_blank" class="splitPageLink"><strong>DOWNLOAD</strong></a><br />
              <font size="1">(right click and do 'save target as')</font>
              <?php } ;?>
              &nbsp; </td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://searchmarketing.yahoo.com/shopsb/index.php" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://login.yahoo.com/config/login?.intl=us&.src=pmshp&.done=https://productsubmit.adcentral.yahoo.com/sspi/us/" target="_blank" class="splitPageLink"><strong>LOGIN</strong></a><a href="http://searchmarketing.yahoo.com/shopsb/index.php" target="_blank" class="splitPageLink"></a></td>
          </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong>BidHopper:</strong></td>
            <td bgcolor="#EFEFEF" class="main"><p>BidHopper is a Free listing and search engine service for the dynamic pricing community. 
  They dynamically index all listed items from members web sites 4 times per day 
  to provide you with the freshest listings possible. </p>
              <p>Your store is already preset for BidHopper services and only requires you to register on their website.</p>
              <p>When prompted during registration for your bidhopper page URL, enter: 	</p>
              <p><a href="<?php echo "http://".$_SERVER['HTTP_HOST'] . DIR_WS_CATALOG . "bidhopper.php"; ?>" target="_blank" class="splitPageLink"><?php echo "http://".$_SERVER['HTTP_HOST'] . DIR_WS_CATALOG . "bidhopper.php"; ?></a></p>
              <p><strong>FREE</strong></p></td>
            <td align="center" bgcolor="#EFEFEF">&nbsp;</td>
            <td align="center" bgcolor="#EFEFEF">&nbsp;</td>
            <td align="center" bgcolor="#EFEFEF"><a href="http://www.bidhopper.com" target="_blank" class="splitPageLink"><strong>SIGN-UP</strong></a></td>
            <td align="center" bgcolor="#EFEFEF">&nbsp;</td>
          </tr>
        </table></td>
      </tr>
      <tr>
        <td class="main"><br />
          * NOTE: Time to view your update products on provider website will vary. Each provider  requires you to register for an account and additional fees may be charged by the provider. See provider websites for further details. </td>
      </tr>
      <tr>
        <td class="pageHeading"><br />
          Troubleshooting</td>
      </tr>
      <tr>
        <td class="main">Here is a list of common issues associated with your store feeds: </td>
      </tr>
      <tr>
        <td class="main"><table width="100%" border="1" cellpadding="2" cellspacing="0" bordercolor="#FFFFFF">
          <tr valign="top" class="main">
            <td width="29%" align="right" bgcolor="#080381" class="menuBoxHeading"><strong> &quot;FTP open connection failed to X&quot; </strong></td>
            <td width="71%" bgcolor="#EFEFEF" class="main"><p><strong>This error, commonly seen with froogle feeds, sometimes with other feeds, and is usually caused by one of several issues:</strong></p>
              <blockquote>
                <p> a. You do not have the proper ftp logins setup within admin of your store for that provider. Click <a href="configuration.php?gID=62" target="_blank" class="splitPageLink">[HERE]</a> to update your 'store feed' accounts, then attempt to run the feed again. </p>
                <p>b. Your web hosting provider has a firewall in place that is preventing remote FTP connection via HTTP protocol. This is very common. Check with your hosting provider to see if they can adjust these settings for the /feeds/ folder on your store so that it may work, then try running the feed again. </p>
              </blockquote>              
              <p><strong>Alternatively you can upload these feeds manually by:</strong></p>
              <p>1. Click the DOWNLOAD link in the appropriate column above, or right click to 'save target as' to save to your desktop. If the DOWNLOAD link does not appear, then you have not yet created your store feed for this provider. </p>
              <p>2. Click on the corresponding LOGIN link to login to your account.</p>
              <p>3. Follow the providers instructions for uploading your store feed file. </p></td>
            </tr>
          <tr valign="top" class="main">
            <td align="right" bgcolor="#080381" class="menuBoxHeading"><strong> &quot;Error writing to file&quot; </strong></td>
            <td bgcolor="#EFEFEF" class="main"><p><strong>This error indicates that you do not have proper permissions setup on your /feeds/ folder. Please ensure that it is set to CHMOD 777 to allow creation / update of the feed files. </strong></p>
              <p>If you are unsure on how to do this, contact your hosting provider. <strong></strong></p></td>
            </tr>
        </table></td>
      </tr>
      <tr>
        <td class="main">&nbsp;</td>
      </tr>
    </table></td>
    <!-- body_text_eof //-->
  </tr>
</table>
<!-- body_eof //-->
<?php
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>

