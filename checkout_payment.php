<?php
/*
  $Id$
  
  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2010 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');

// if the customer is not logged on, redirect them to the login page
  if (!tep_session_is_registered('customer_id')) {
    $navigation->set_snapshot();
    tep_redirect(tep_href_link('login.php', '', 'SSL'));
  }

// if there is nothing in the customers cart, redirect them to the shopping cart page
  if ($cart->count_contents() < 1) {
    tep_redirect(tep_href_link('shopping_cart.php'));
  }

// if no shipping method has been selected, redirect the customer to the shipping method selection page
  if (!tep_session_is_registered('shipping')) {
    tep_redirect(tep_href_link('checkout_shipping.php', '', 'SSL'));
  }

// avoid hack attempts during the checkout procedure by checking the internal cartID
  if (isset($cart->cartID) && tep_session_is_registered('cartID')) {
    if ($cart->cartID != $cartID) {
      tep_redirect(tep_href_link('checkout_shipping.php', '', 'SSL'));
    }
  }

// Stock Check
  if ( (STOCK_CHECK == 'true') && (STOCK_ALLOW_CHECKOUT != 'true') ) {
    $products = $cart->get_products();
    for ($i=0, $n=sizeof($products); $i<$n; $i++) {
      if (tep_check_stock($products[$i]['id'], $products[$i]['quantity'])) {
        tep_redirect(tep_href_link('shopping_cart.php'));
        break;
      }
    }
  }

// if no billing destination address was selected, use the customers own address as default
  if (!tep_session_is_registered('billto')) {
    tep_session_register('billto');
    $billto = $customer_default_address_id;
  } else {
// verify the selected billing address
    if ( (is_array($billto) && empty($billto)) || is_numeric($billto) ) {
      $check_address_query = tep_db_query("select count(*) as total from " . TABLE_ADDRESS_BOOK . " where customers_id = '" . (int)$customer_id . "' and address_book_id = '" . (int)$billto . "'");
      $check_address = tep_db_fetch_array($check_address_query);

      if ($check_address['total'] != '1') {
        $billto = $customer_default_address_id;
        if (tep_session_is_registered('payment')) tep_session_unregister('payment');
      }
    }
  }

  require('includes/classes/order.php');
  $order = new order;

  if (!tep_session_is_registered('comments')) tep_session_register('comments');
  if (isset($_POST['comments']) && @tep_not_null($_POST['comments'])) {
    $comments = tep_db_prepare_input($_POST['comments']);
  }

  $total_weight = $cart->show_weight();
  $total_count = $cart->count_contents();

// load all enabled payment modules
  require('includes/classes/payment.php');
  $payment_modules = new payment;

  require('includes/languages/' . $language . '/checkout_payment.php');

  $breadcrumb->add(NAVBAR_TITLE_1, tep_href_link('checkout_shipping.php', '', 'SSL'));
  $breadcrumb->add(NAVBAR_TITLE_2, tep_href_link('checkout_payment.php', '', 'SSL'));

  require('includes/template_top.php');
?>

<?php echo $payment_modules->javascript_validation(); ?>

<div class="page-header">
  <h1 class="h3"><?php echo HEADING_TITLE; ?></h1>
</div>
<?php
if (isset($xmasMessage)){?>
<div class="alert alert-danger">
	<?php echo $xmasMessage; ?>
	<div class="clearfix"></div>
</div><?php } ?>
<?php echo tep_draw_form('checkout_payment', tep_href_link('checkout_confirmation.php', '', 'SSL'), 'post', 'class="form-horizontal" onsubmit="return check_form();" id="paymentMethodsForm"', true); ?>

<div class="contentContainer">

<?php
  if (isset($_GET['payment_error']) && is_object(${$_GET['payment_error']}) && ($error = ${$_GET['payment_error']}->get_error())) {
?>

  <div class="contentText">
    <?php echo '<strong>' . tep_output_string_protected($error['title']) . '</strong>'; ?>

    <p class="messageStackError"><?php echo tep_output_string_protected($error['error']); ?></p>
  </div>

<?php
  }
?>

  <h2 class="h3"><?php echo TABLE_HEADING_BILLING_ADDRESS; ?></h2>

  <div class="contentText row">
    <div class="col-sm-8">
      <div class="alert alert-warning">
        <?php echo TEXT_SELECTED_BILLING_DESTINATION; ?>
        <div class="clearfix"></div>
        <div class="pull-right">
          <?php echo tep_draw_button(IMAGE_BUTTON_CHANGE_ADDRESS, 'fa fa-home', tep_href_link('checkout_payment_address.php', '', 'SSL')); ?>
        </div>
        <div class="clearfix"></div>
      </div>
    </div>
    <div class="col-sm-4">
      <div class="panel panel-primary">
        <div class="panel-heading"><?php echo TITLE_BILLING_ADDRESS; ?></div>
        <div class="panel-body">
          <?php echo tep_address_label($customer_id, $billto, true, ' ', '<br />'); ?>
        </div>
      </div>
    </div>
  </div>

 <?php /* <div class="clearfix"></div> */?>

  <h2 class="h3"><?php echo TABLE_HEADING_PAYMENT_METHOD; ?></h2>

<?php
  $selection = $payment_modules->selection();

  if (sizeof($selection) > 1) {
?>

  <div class="contentText">
    <div class="alert alert-warning">
      <div class="row">
        <div class="col-xs-8">
          <?php echo TEXT_SELECT_PAYMENT_METHOD; ?>
        </div>
        <div class="col-xs-4 text-right">
          <?php echo '<strong>' . TITLE_PLEASE_SELECT . '</strong>'; ?>
        </div>
      </div>
    </div>
  </div>
 <div class="contentText">
    <div id="paymentError" class="alert alert-danger" style="display:none"></div>
  </div>

<?php
    } else {
?>

  <div class="contentText">
    <div class="alert alert-info"><?php echo TEXT_ENTER_PAYMENT_INFORMATION; ?></div>
  </div>
 

<?php
    }
?>

  <div id="paymentAccordian" class="contentText">

<?php
  $radio_buttons = 0;
  for ($i=0, $n=sizeof($selection); $i<$n; $i++) {
?><br style="clear:both"><div style="float:left;valign:middle"><?php 
			// modified by mark to allow hiding of different payment options to prevent customertrs filling in the wrong fields
				if (sizeof($selection) > 1) {
					echo tep_draw_radio_field('payment', $selection[$i]['id'],'','id=' . '"paymentItem' . $i . '" onclick="setAccord(' . $i . ')"');
				} else {
					echo tep_draw_hidden_field('payment', $selection[$i]['id']);
				}
			?></div><div class="panel panel-default" style="margin-left:25px;"><div class="accordHeader panel-heading" style="font-size: 14px;" id=<?php echo '"' . $selection[$i]['id'] . 'Header"'?> colspan="5"><b><?php echo $selection[$i]['module']; ?></b></div>
		<div class="panel-body">
			<?php
				if (isset($selection[$i]['error'])) {
			?>
						   <?php echo $selection[$i]['error']; ?>
						   
			<?php
				} elseif (isset($selection[$i]['fields']) && is_array($selection[$i]['fields'])) {
			?>
							  
								  <?php // modified by mark to allow hiding of different payment options to prevent customertrs filling in the wrong fields  ?>
			<table width="100%" border="0" id=<?php echo '"' . $selection[$i]['id'] . 'row"'?> cellspacing="0"  cellpadding="2">
			<?php
				  for ($j=0, $n2=sizeof($selection[$i]['fields']); $j<$n2; $j++) {
			?>
					<tr>
						<td>
						<div class="col-lg-6"><?php echo $selection[$i]['fields'][$j]['title']; ?></div>
						<div class="col-lg-6"><?php echo $selection[$i]['fields'][$j]['field']; ?></div>
						</dt>
					</tr>
			<?php
				}
			?>
			</table>
			<?php
			}
			?>
</div></div>


<?php
    $radio_buttons++;
  }
?>
<div class="alert alert-info">Surcharge is applied to credit card payments only, other payment methods such debit card, bank transfer etc are not subject to surcharge.</div>
<div class="alert alert-success">**With ref to <strong>Consumer Rights Regulations</strong> (payment of surcharges), the ban on surcharges does not apply to payments made using commercial, business or corporate credit cards and to business to business contracts. Most of our goods are for business and commercial sector but if your purchase is consumer related then please state in <strong>comments section</strong> below in order to avoid surcharges as we can offer alternative payment options such as; payment by proforma invoice, debit card, bank transfer or cheque. To checkout for now, select “Call me to take payment details option.</div>
  </div>

  <hr>

  <div class="contentText">
    <div class="form-group">
      <label for="inputComments" class="control-label col-sm-4"><?php echo TABLE_HEADING_COMMENTS; ?></label>
      <div class="col-sm-8">
        <?php
        echo tep_draw_textarea_field('comments', 'soft', 60, 5, $comments, 'id="inputComments" placeholder="' . TABLE_HEADING_COMMENTS . '"');
        ?>
      </div>
    </div>
  </div>

  <div class="buttonSet">
    <div class="text-right"><?php echo tep_draw_button(IMAGE_BUTTON_CONTINUE, 'fa fa-angle-right', null, 'primary', null, 'btn-success'); ?></div>
  </div>

  <div class="clearfix"></div>

  <div class="contentText">
    <div class="stepwizard">
      <div class="stepwizard-row">
        <div class="stepwizard-step">
          <a href="<?php echo tep_href_link('checkout_shipping.php', '', 'SSL'); ?>"><button type="button" class="btn btn-default btn-circle">1</button></a>
          <p><a href="<?php echo tep_href_link('checkout_shipping.php', '', 'SSL'); ?>"><?php echo CHECKOUT_BAR_DELIVERY; ?></a></p>
        </div>
        <div class="stepwizard-step">
          <button type="button" class="btn btn-primary btn-circle">2</button>
          <p><?php echo CHECKOUT_BAR_PAYMENT; ?></p>
        </div>
        <div class="stepwizard-step">
          <button type="button" class="btn btn-default btn-circle" disabled="disabled">3</button>
          <p><?php echo CHECKOUT_BAR_CONFIRMATION; ?></p>
        </div>
      </div>
    </div>
  </div>

</div>

</form>
<div class="modal fade" id="paymentMethodChangeModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Change Payment Method</h4>
      </div>
      <div class="modal-body">
        <p>You have entered Credit/Debit card details, if you change to another payment method they will be lost upon clicking Continue.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" id="keepCCBtn" data-dismiss="modal">Keep Credit Card Selected</button>
        <button type="button" class="btn btn-primary" data-dismiss="modal">Change Payment Method</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="modal fade" id="ccErrorModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">Change Payment Method</h4>
      </div>
      <div class="modal-body">
        <p>There are errors on your credit card entry please check and try again.</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<?php
  require('includes/template_bottom.php');
  require('includes/application_bottom.php');
?>
