<?php
/*
  CSRF Protection Test Page
  
  This page tests the CSRF protection implementation
*/

require('includes/application_top.php');

$test_results = array();
$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    require_once('includes/functions/csrf_middleware.php');
    
    if (isset($_POST['test_action'])) {
        switch ($_POST['test_action']) {
            case 'test_csrf_validation':
                // Test CSRF validation
                if (tep_csrf_check_token('test_form')) {
                    $success_message = 'CSRF validation passed successfully!';
                } else {
                    $error_message = 'CSRF validation failed!';
                }
                break;
                
            case 'test_legacy_token':
                // Test legacy sessiontoken validation
                if (tep_validate_form_token($_POST)) {
                    $success_message = 'Legacy token validation passed successfully!';
                } else {
                    $error_message = 'Legacy token validation failed!';
                }
                break;
        }
    }
}

// Generate test data
$csrf_token = tep_csrf_token('test_form');
$csrf_field = tep_csrf_token_field('test_form');
$csrf_meta = tep_csrf_meta_tag('test_form');

require('includes/template_top.php');
?>

<div class="container mt-4">
    <h1>CSRF Protection Test Page</h1>
    
    <?php if ($error_message): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <?php if ($success_message): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <div class="row">
        <div class="col-md-6">
            <h3>CSRF Token Information</h3>
            <table class="table table-bordered">
                <tr>
                    <td><strong>CSRF Protection Enabled:</strong></td>
                    <td><?php echo tep_csrf_protection_enabled() ? 'Yes' : 'No'; ?></td>
                </tr>
                <tr>
                    <td><strong>Generated CSRF Token:</strong></td>
                    <td><code><?php echo htmlspecialchars($csrf_token); ?></code></td>
                </tr>
                <tr>
                    <td><strong>Session Token (Legacy):</strong></td>
                    <td><code><?php echo htmlspecialchars($sessiontoken); ?></code></td>
                </tr>
                <tr>
                    <td><strong>Session ID:</strong></td>
                    <td><code><?php echo htmlspecialchars(session_id()); ?></code></td>
                </tr>
            </table>
        </div>
        
        <div class="col-md-6">
            <h3>Test Forms</h3>
            
            <!-- Test CSRF Token Form -->
            <div class="card mb-3">
                <div class="card-header">Test CSRF Token Validation</div>
                <div class="card-body">
                    <form method="POST" class="csrf-protected">
                        <?php echo tep_draw_csrf_token_field('test_form'); ?>
                        <input type="hidden" name="test_action" value="test_csrf_validation">
                        <button type="submit" class="btn btn-primary">Test CSRF Validation</button>
                    </form>
                </div>
            </div>
            
            <!-- Test Legacy Token Form -->
            <div class="card mb-3">
                <div class="card-header">Test Legacy Token Validation</div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="formid" value="<?php echo $sessiontoken; ?>">
                        <input type="hidden" name="test_action" value="test_legacy_token">
                        <button type="submit" class="btn btn-secondary">Test Legacy Token</button>
                    </form>
                </div>
            </div>
            
            <!-- Test Form Without Token (Should Fail) -->
            <div class="card mb-3">
                <div class="card-header">Test Without Token (Should Fail)</div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="test_action" value="test_csrf_validation">
                        <button type="submit" class="btn btn-danger">Test Without Token</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <h3>JavaScript CSRF Test</h3>
            <button id="test-ajax-csrf" class="btn btn-info">Test AJAX with CSRF</button>
            <button id="test-ajax-no-csrf" class="btn btn-warning">Test AJAX without CSRF</button>
            <div id="ajax-results" class="mt-3"></div>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-12">
            <h3>Generated HTML Elements</h3>
            <h5>CSRF Token Field:</h5>
            <pre><code><?php echo htmlspecialchars($csrf_field); ?></code></pre>
            
            <h5>CSRF Meta Tag:</h5>
            <pre><code><?php echo htmlspecialchars($csrf_meta); ?></code></pre>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Test AJAX with CSRF
    $('#test-ajax-csrf').click(function() {
        $.ajax({
            url: 'api.php',
            method: 'POST',
            data: {
                getCSRFToken: 'test_ajax'
            },
            success: function(response) {
                $('#ajax-results').html('<div class="alert alert-success">AJAX with CSRF successful: ' + JSON.stringify(response) + '</div>');
            },
            error: function(xhr, status, error) {
                $('#ajax-results').html('<div class="alert alert-danger">AJAX with CSRF failed: ' + error + '</div>');
            }
        });
    });
    
    // Test AJAX without CSRF (should fail for protected endpoints)
    $('#test-ajax-no-csrf').click(function() {
        // Temporarily disable CSRF for this request
        $.ajaxSetup({
            beforeSend: function(xhr) {
                // Don't add CSRF token
            }
        });
        
        $.ajax({
            url: 'api.php',
            method: 'POST',
            data: {
                sendEmail: {
                    name: 'Test',
                    email: '<EMAIL>',
                    message: 'Test message'
                }
            },
            success: function(response) {
                $('#ajax-results').html('<div class="alert alert-warning">AJAX without CSRF unexpectedly succeeded: ' + JSON.stringify(response) + '</div>');
            },
            error: function(xhr, status, error) {
                if (xhr.status === 403) {
                    $('#ajax-results').html('<div class="alert alert-success">AJAX without CSRF correctly failed with 403 Forbidden</div>');
                } else {
                    $('#ajax-results').html('<div class="alert alert-danger">AJAX without CSRF failed with unexpected error: ' + error + '</div>');
                }
            }
        });
        
        // Reset AJAX setup
        $.ajaxSetup({
            beforeSend: function(xhr) {
                // Re-enable CSRF
            }
        });
    });
});
</script>

<?php
require('includes/template_bottom.php');
require('includes/application_bottom.php');
?>
