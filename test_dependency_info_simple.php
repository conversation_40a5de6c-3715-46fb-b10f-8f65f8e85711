<?php
require_once("includes/application_top.php");
require_once("includes/classes/attributes.class.php");
require_once("includes/functions/tcs_attributes_components.php");

// Get product ID from URL parameter, default to 1 for testing
$product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 1;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dependency Issues with Info Buttons - Simple Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .panel {
            margin-bottom: 20px;
        }
        .test-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Dependency Issues with Info Buttons - Simple Test</h1>
        
        <div class="test-info">
            <h4><i class="fa fa-info-circle"></i> Test Information</h4>
            <p><strong>Current Product ID:</strong> <?= $product_id ?></p>
            <p><strong>Purpose:</strong> This page demonstrates the info button functionality using vanilla JavaScript (no Alpine.js).</p>
            <p><strong>How to test:</strong> Click the blue ℹ️ buttons next to any dependency issues to see detailed explanations.</p>
            
            <div class="btn-group" style="margin-top: 10px;">
                <a href="?product_id=1" class="btn btn-sm btn-default">Test Product 1</a>
                <a href="?product_id=2" class="btn btn-sm btn-default">Test Product 2</a>
                <a href="?product_id=3" class="btn btn-sm btn-default">Test Product 3</a>
                <a href="?product_id=5" class="btn btn-sm btn-default">Test Product 5</a>
            </div>
        </div>

        <?php
        try {
            // Create an instance of the attributes class
            $products_attributes = new tcs_product_attributes($product_id);
            
            // Get and display dependency warnings with info buttons
            echo tcs_draw_attributes_dependency_warnings($products_attributes);
            
            // Also show raw issues for debugging
            $issues = $products_attributes->check_dependency_issues();
            
            if (!empty($issues)) {
                echo '<div class="debug-info">';
                echo '<h5>Debug Information (Raw Issues Data):</h5>';
                echo '<pre>' . print_r($issues, true) . '</pre>';
                echo '</div>';
            } else {
                echo '<div class="alert alert-success">';
                echo '<h4>✅ No Dependency Issues Found</h4>';
                echo '<p>Product ' . $product_id . ' has no dependency issues. Try testing with a different product ID that has known issues.</p>';
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="alert alert-danger">';
            echo '<h4>Error Testing Dependency Issues</h4>';
            echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo '<p><strong>Product ID:</strong> ' . $product_id . '</p>';
            echo '</div>';
        }
        ?>
        
        <div class="panel panel-default" style="margin-top: 30px;">
            <div class="panel-header">
                <div class="panel-body">Features Implemented</div>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <h5>✅ Completed Features:</h5>
                        <ul>
                            <li><strong>Info Buttons:</strong> Blue ℹ️ buttons next to each issue</li>
                            <li><strong>Interactive Modals:</strong> Click buttons to see detailed explanations</li>
                            <li><strong>Issue Type Icons:</strong> Visual indicators for different issue types</li>
                            <li><strong>Detailed Explanations:</strong> "What this means" and "How to resolve" sections</li>
                            <li><strong>Vanilla JavaScript:</strong> No framework dependencies</li>
                            <li><strong>Responsive Design:</strong> Works on desktop and mobile devices</li>
                            <li><strong>Click Outside to Close:</strong> User-friendly modal interaction</li>
                        </ul>
                        
                        <h5>🔧 Technical Implementation:</h5>
                        <ul>
                            <li><strong>Simple JavaScript:</strong> Uses basic onclick handlers and DOM manipulation</li>
                            <li><strong>CSS Styling:</strong> Custom CSS for modal appearance and animations</li>
                            <li><strong>Cross-browser Compatible:</strong> Works in all modern browsers</li>
                            <li><strong>No External Dependencies:</strong> Doesn't require Alpine.js or other frameworks</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Add keyboard support for modals
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // Close any open modals
                var modals = document.querySelectorAll('[id^="issue_modal_"]');
                modals.forEach(function(modal) {
                    if (modal.style.display === 'flex') {
                        modal.style.display = 'none';
                    }
                });
            }
        });
        
        // Ensure modals display as flex when opened
        function openModal(modalId) {
            var modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'flex';
            }
        }
        
        function closeModal(modalId) {
            var modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
