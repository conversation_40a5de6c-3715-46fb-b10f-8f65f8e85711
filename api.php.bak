<?php
define('START_TIME',microtime(true));
require('includes/application_top.php');
$time[] = "API top execution time: " . etime() . " seconds";


function etime(){
    return microtime(true) - START_TIME;
};

header('Access-Control-Allow-Origin: ' . HTTPS_SERVER);
header('Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept');
$input_params = array_merge($_GET,$_POST);
	if (isset($input_params['getProd'])) {
		$response = getProd($input_params['id']);
		echo json_encode($response);
	} elseif (isset($input_params['sendEmail'])) {
		$response = sendEmail($input_params);
		echo json_encode($response);
	} elseif (isset($input_params['adminViewFamilies'])) {
		$response = adminViewFamilies($input_params['id'], $input_params['family_id']);
		echo json_encode($response);
	} elseif (isset($input_params['getNewData'])) {
		$response = getNewData($input_params['getNewData'], $input_params['attributes'], $input_params['selected_attribute']);
		echo $response;
	} elseif (isset($input_params['addToCart'])) {
		require_once('includes/classes/attributes.class.php');
		require_once('includes/classes/shopping_cart.php');
		$response = addToCart($input_params['products_id'],$input_params['products_name'],$input_params['attributes']);
		echo $response;
	} elseif (isset($input_params['update_cart_quantity'])) {
		require_once('includes/classes/attributes.class.php');
		require_once('includes/classes/shopping_cart.php');
		$response = update_cart_quantity($input_params['products']);
		echo json_encode($response);
	}

function getProd($id) {
    $response = '';
    $input = tep_db_prepare_input($id);
    $input = tep_db_input($input);

    if (is_numeric($input)) {
        $sqlQuery = "SELECT * FROM products p, products_description pd WHERE p.products_id = '" . $input . "' AND p.products_id = pd.products_id";
        $sql = tep_db_query($sqlQuery);
        $response = array();

        if ($rows = tep_db_fetch_array($sql)) {
            extract($rows);
            $response = array(
                "product" => array(
                    "name" => $products_name,
                    "id" => $products_id,
                    "model" => $products_model,
                    "price" => $products_price
                )
            );
        }
    }
    return $response;
}

function sendEmail($postData) {
    $response = '';

    $name = $postData['name'];
    $company = $postData['company'];
    $address = $postData['address'];
    $telephone = $postData['telephone'];
    $fax       = $postData['fax'];
    $email = $postData['email'];
    $capture = $postData['capture'];
    $message = $postData['message'];
    $captureCorrect = 0;

    if (strtolower(str_replace(' ', '', $capture)) == 'aekt') {
        $form_details =  "Name: " . $name . "\nCompany: " . $company . "\nAddress: " . $address . "\nPhone: " . $telephone . "\nFax: " . $fax . "\nE-Mail: " .  $email . "\nMessage: \n" . $message;
        $recipient = '<EMAIL>';
        $subject = 'Enquiry - CADandBIM.co.uk Site';
        $from = $email;
        mail($recipient, $subject, $form_details, "FROM: $from \n");
    }

    return $response;
}

function adminViewFamilies($id, $familyId) {
    $response = '';

    if (is_numeric($id)) {
        $sql = tep_db_query("SELECT distinct products_id FROM products_families WHERE family_id = '" . $familyId . "'");
        $response = array();

        if ($rows = tep_db_fetch_array($sql)) {
            extract($rows);
            $response = array(
                "familyItems" => array(
                    "id" => $products_id
                )
            );
        }
    }
    return $response;
}

function getNewData($productId, $current_attributes, $selected_attribute = array()) {
    $time[] = "API getNewData execution time: " . etime() . " seconds";
    $languages_id = 1;
    $selected_array = is_string($selected_attribute) ? json_decode(stripslashes($selected_attribute), true) : $selected_attribute;
    $attrib_array = is_string($current_attributes) ? json_decode(stripslashes($current_attributes), true) : $current_attributes;

    $selected_attributes = array_replace($attrib_array,$selected_array);
    $products_attributes = new tcs_product_attributes($productId,1,$selected_attributes);
    if ($products_attributes->has_attributes){
        $products_attributes->get_attributes();
        if ($products_attributes->has_variations) $products_attributes->get_variations();
    }

    $time[] = "API getNewData after products_attributes execution time: " . etime() . " seconds";
    $product_info_query_sql = "
    SELECT
            p.products_id,
            pd.products_name,
            pd.products_description,
            IF(p2c.products_to_categories_attribs_id, p2ca.products_model, p.products_model) AS products_model,
            p.products_quantity,
            p.products_image,
            pd.products_url,
            if (pa.products_id, pa.options_values_price + p.products_price, p.products_price) as products_price,
            p.products_tax_class_id,
            p.products_date_added,
            p.products_date_available,
            p.manufacturers_id,
            p.products_gtin
    FROM
        products p left join products_attributes pa on p.products_id = pa.products_id and pa.attribute_default = 1
        JOIN products_description pd ON pd.products_id = p.products_id
        JOIN products_to_categories p2c ON p2c.products_id = p.products_id
        LEFT JOIN products_to_categories_attrib p2ca on p2c.products_to_categories_attribs_id = p2ca.products_to_categories_attribs_id

    WHERE
        p.products_status = '1'
        AND p.products_id = '" . (int)$productId . "'
        AND pd.language_id = '" . (int)$languages_id . "'";

    $product_info_query = tep_db_query($product_info_query_sql);
    $time[] = "API getNewData after product_info_query execution time: " . etime() . " seconds";

    $product_info = tep_db_fetch_array($product_info_query);
    include('includes/languages/english/modules/content/product_info/cm_pi_options_attributes.php');
    include('includes/languages/english/modules/content/product_info/cm_pi_price.php');
    include('includes/languages/english/modules/content/product_info/cm_pi_model.php');
    include('includes/languages/english/modules/content/product_info/cm_pi_buy_button.php');
    include('includes/languages/english/modules/content/product_info/cm_pi_name.php');

    include('includes/modules/content/product_info/cm_pi_options_attributes.php');
    include('includes/modules/content/product_info/cm_pi_price.php');
    include('includes/modules/content/product_info/cm_pi_model.php');
    include('includes/modules/content/product_info/cm_pi_buy_button.php');
    include('includes/modules/content/product_info/cm_pi_name.php');

    // Build the attribute selector first to ensure attributes are properly processed
    $attributes_selector = cm_pi_options_attributes::build_attribute_selector(1, $currencies, $product_info, $products_attributes);
    $time[] = "API getNewData after options attributes execution time: " . etime() . " seconds";

    // Update the products_attributes object with the selected attributes from the selector
    $products_attributes->selected_attributes = $attributes_selector['selected_attributes'];

    // Now build the rest of the UI with the updated attributes
    $response = cm_pi_name::build_name_ui($product_info,$products_attributes);
    $time[] = "API getNewData after name execution time: " . etime() . " seconds";
    $response .= cm_pi_model::build_model_ui($product_info,$products_attributes,$products_attributes->selected_attributes);
    $time[] = "API getNewData after model execution time: " . etime() . " seconds";
    $response .= cm_pi_price::build_price_ui($product_info,$products_attributes->selected_attributes);
    $time[] = "API getNewData after price execution time: " . etime() . " seconds";
    $response .= cm_pi_buy_button::build_buy_button_ui($product_info,1,$products_attributes);
    $time[] = "API getNewData after buy button execution time: " . etime() . " seconds";
    $response .= $attributes_selector['html'];

    return $response;
}

//[{"Success":1,"qty":1,"products_id":11165,"Name":"HP No.712 Ink Cartridge, Black, 38ml, Single Pack","new_cart_total_items":3,"new_cart_total_price":596}]
function addToCart($products_id, $products_name, $attributes) {
    global $cart, $currencies;

    $products_attributes = new tcs_product_attributes($products_id, 1, $attributes);

    $cart->add_cart($products_id, $cart->get_quantity(tep_get_uprid($products_id, $attributes)) + 1, $attributes);

    include('includes/languages/english/modules/content/product_info/cm_pi_buy_button.php');
    include('includes/languages/english/modules/navbar_modules/nb_shopping_cart.php');

    include('includes/modules/content/product_info/cm_pi_buy_button.php');
    include('includes/modules/navbar_modules/nb_shopping_cart.php');

    // Return the updated cart HTML and trigger a custom event with product name
    header('HX-Trigger: {"cartUpdate": {"productName": "' . addslashes($products_name . $products_attributes->generate_product_suffix($attributes)) . '"}}'); // Use addslashes for safety
    return nb_shopping_cart::build_cart_dropdown();
}

function update_cart_quantity($products_json){
    global $cart, $currencies;
    $attributes = "";
    $products = json_decode(stripslashes($products_json), true);
    for ($i = 0; $i < sizeof($products); $i++) {
        $products_id = $products[$i]['id'];
        $products_qty = $products[$i]['qty'];
        if (strpos($products_id, "{") > 0) {
            $attribute_string = '{' . explode('{', $products_id, 2)[1];
            $products_attributes = new tcs_product_attributes($products_id, 1, $attribute_string);
            $attributes = $products_attributes->parse_attribute_string($attribute_string);
        }
        $cart->add_cart($products_id, $products_qty, $attributes, false);
    }

    $cart_products = $cart->get_products();
    for ($i = 0; $i < sizeof($cart_products); $i++) {
        $products_id = $cart_products[$i]['id'];
        $products_qty = $cart_products[$i]['quantity'];
        $attributes = $cart_products[$i]['attributes'];
        $products_out[$i] = array(
            "id" => $products_id,
            "qty" => $products_qty,
            "attributes" => $attributes,
            "final_price" => $currencies->display_price($cart_products[$i]['final_price'], tep_get_tax_rate($cart_products[$i]['tax_class_id']), $cart_products[$i]['quantity'])
        );
    }

    $response[] = array(
        "Success" => 1,
        "products_qty" => $cart->get_quantity(tep_get_uprid($products_id, $attributes)),
        "products" => $products_out,
        "new_cart_total_price" => $currencies->format($cart->show_total())
    );
    return $response;
}
$time[] = "API execution time: " . etime() . " seconds";
print_rr(implode("\n",$time),'time');
